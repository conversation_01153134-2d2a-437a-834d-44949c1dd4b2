"""
金豆发放申请校验系统 - 工具类模块
提供系统所需的通用工具函数和辅助类

本模块包含以下核心工具类：
1. ConfigManager: 配置文件管理器，负责加载和解析YAML配置
2. LoggerSetup: 日志系统设置器，统一配置日志格式和输出
3. FileManager: 文件管理器，处理文件操作和目录管理
4. DataValidator: 数据验证器，提供各种数据格式验证功能
5. PerformanceMonitor: 性能监控器，监控系统运行性能

设计原则：
- 单一职责：每个类专注于特定功能
- 可重用性：工具类可在多个模块中重用
- 配置驱动：行为通过配置文件控制
- 异常安全：提供完善的异常处理机制

"""

# 导入标准库模块
import os  # 操作系统接口
import re  # 正则表达式
import logging  # 日志记录
import pandas as pd  # 数据处理
import yaml  # YAML配置文件解析
from datetime import datetime  # 日期时间处理
from pathlib import Path  # 路径操作
from typing import Dict, List, Any, Optional, Tuple  # 类型注解
import shutil  # 高级文件操作


class ConfigManager:
    """
    配置管理器
    
    负责加载、解析和管理系统配置文件。支持：
    - YAML格式配置文件的加载
    - 嵌套配置项的访问（如 'data_paths.raw_data'）
    - 配置项的默认值处理
    - 配置文件格式验证
    
    使用示例：
        config = ConfigManager("config.yaml")
        raw_path = config.get('data_paths.raw_data', 'default_path')
    """
    
    def __init__(self, config_path: str = "config.yaml"):
        """
        初始化配置管理器
        
        Args:
            config_path: 配置文件路径，默认为"config.yaml"
            
        初始化过程：
        1. 保存配置文件路径
        2. 加载并解析配置文件
        3. 验证配置文件格式
        """
        self.config_path = config_path  # 保存配置文件路径
        self.config = self._load_config()  # 加载配置内容
    
    def _load_config(self) -> Dict[str, Any]:
        """
        加载配置文件
        
        使用UTF-8编码读取YAML配置文件，并进行安全解析
        
        Returns:
            Dict[str, Any]: 解析后的配置字典
            
        Raises:
            FileNotFoundError: 当配置文件不存在时
            yaml.YAMLError: 当配置文件格式错误时
            
        安全性：
        - 使用yaml.safe_load避免执行恶意代码
        - 使用UTF-8编码确保中文配置正确读取
        """
        try:
            # 使用UTF-8编码打开配置文件
            with open(self.config_path, 'r', encoding='utf-8') as file:
                # 使用安全的YAML加载器解析配置
                config = yaml.safe_load(file)
                # 记录成功加载的日志
                logging.info(f"成功加载配置文件: {self.config_path}")
                return config
        except FileNotFoundError:
            # 配置文件不存在时的错误处理
            logging.error(f"配置文件不存在: {self.config_path}")
            raise  # 重新抛出异常，让调用者处理
        except yaml.YAMLError as e:
            # YAML格式错误时的错误处理
            logging.error(f"配置文件格式错误: {e}")
            raise  # 重新抛出异常，让调用者处理
    
    def get(self, key_path: str, default: Any = None) -> Any:
        """
        获取配置值，支持嵌套键路径
        
        支持使用点号分隔的路径访问嵌套配置项，例如：
        - 'logging.level' 访问 config['logging']['level']
        - 'data_paths.raw_data' 访问 config['data_paths']['raw_data']
        
        Args:
            key_path: 配置键路径，使用点号分隔嵌套层级
            default: 当配置项不存在时返回的默认值
            
        Returns:
            Any: 配置值或默认值
            
        示例：
            level = config.get('logging.level', 'INFO')
            path = config.get('data_paths.raw_data', './data')
        """
        try:
            # 将键路径按点号分割为键列表
            keys = key_path.split('.')
            # 从根配置开始遍历
            value = self.config
            # 逐层访问嵌套配置
            for key in keys:
                value = value[key]  # 获取下一层配置
            return value  # 返回最终配置值
        except (KeyError, TypeError):
            # 当键不存在或类型错误时，返回默认值
            return default


class LoggerSetup:
    """
    日志设置器
    
    提供统一的日志配置功能，支持：
    - 文件和控制台双重输出
    - 按日期自动分割日志文件
    - 可配置的日志级别和格式
    - UTF-8编码支持中文日志
    - 避免重复添加处理器
    
    设计特点：
    - 静态方法设计，无需实例化
    - 自动创建日志目录
    - 文件名包含日期，便于管理
    - 不同级别的日志分别输出到文件和控制台
    """
    
    @staticmethod
    def setup_logger(name: str, config: Dict[str, Any]) -> logging.Logger:
        """
        设置日志记录器
        
        创建一个配置完整的日志记录器，包括文件和控制台输出。
        文件日志记录所有级别的信息，控制台只显示INFO及以上级别。
        
        Args:
            name: 日志记录器名称，通常使用模块名或类名
            config: 日志配置字典，包含level、format等配置项
            
        Returns:
            logging.Logger: 配置完成的日志记录器实例
            
        配置项说明：
            - level: 日志级别（DEBUG, INFO, WARNING, ERROR, CRITICAL）
            - format: 日志格式字符串
            
        日志文件命名规则：
            logs/{name}_{YYYYMMDD}.log
        """
        # 获取或创建指定名称的日志记录器
        logger = logging.getLogger(name)
        # 设置日志级别，从配置中获取，默认为INFO
        logger.setLevel(getattr(logging, config.get('level', 'INFO')))
        
        # 避免重复添加处理器（如果已经配置过则直接返回）
        if logger.handlers:
            return logger
        
        # 创建日志目录（如果不存在）
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)  # exist_ok=True表示目录存在时不报错
        
        # ========== 配置文件处理器 ==========
        # 生成日志文件名，格式：{name}_{YYYYMMDD}.log
        log_file = log_dir / f"{name}_{datetime.now().strftime('%Y%m%d')}.log"
        # 创建文件处理器，使用UTF-8编码支持中文
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        # 文件处理器记录所有级别的日志（DEBUG及以上）
        file_handler.setLevel(logging.DEBUG)
        
        # ========== 配置控制台处理器 ==========
        # 创建控制台处理器，输出到标准输出
        console_handler = logging.StreamHandler()
        # 控制台只显示INFO及以上级别的日志，避免过多调试信息
        console_handler.setLevel(logging.INFO)
        
        # ========== 配置日志格式化器 ==========
        # 从配置中获取日志格式，如果没有配置则使用默认格式
        formatter = logging.Formatter(config.get('format', 
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
        # 为两个处理器设置相同的格式化器
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        # ========== 添加处理器到日志记录器 ==========
        logger.addHandler(file_handler)    # 添加文件处理器
        logger.addHandler(console_handler)  # 添加控制台处理器
        
        return logger  # 返回配置完成的日志记录器


class FileManager:
    """文件管理器"""
    
    def __init__(self, config_manager: ConfigManager):
        """
        初始化文件管理器
        
        Args:
            config_manager: 配置管理器实例
        """
        self.config = config_manager
        self.logger = LoggerSetup.setup_logger('FileManager', 
                                             config_manager.get('logging', {}))
    
    def ensure_directories(self) -> None:
        """确保所有必要的目录存在"""
        directories = [
            self.config.get('data_paths.raw_data'),
            self.config.get('data_paths.backup_data'),
            self.config.get('data_paths.processed_data'),
            self.config.get('data_paths.reports'),
            self.config.get('data_paths.logs')
        ]
        
        for directory in directories:
            if directory:
                Path(directory).mkdir(parents=True, exist_ok=True)
                self.logger.debug(f"确保目录存在: {directory}")
    
    def find_detail_tables(self) -> List[str]:
        """
        查找所有申请明细表文件
        
        Returns:
            申请明细表文件路径列表
        """
        raw_data_path = Path(self.config.get('data_paths.raw_csv'))
        pattern = self.config.get('table_files.detail_table_pattern')
        
        detail_files = []
        for file_path in raw_data_path.glob("*.csv"):
            if pattern in file_path.name:
                detail_files.append(str(file_path))
                self.logger.info(f"发现申请明细表: {file_path.name}")
        
        self.logger.info(f"共发现 {len(detail_files)} 个申请明细表文件")
        return detail_files
    
    def backup_file(self, source_file: str, backup_dir: str = None) -> str:
        """
        备份文件
        
        Args:
            source_file: 源文件路径
            backup_dir: 备份目录，默认使用配置中的备份目录
            
        Returns:
            备份文件路径
            
        Raises:
            FileNotFoundError: 源文件不存在
            IOError: 备份操作失败
        """
        try:
            if backup_dir is None:
                backup_dir = self.config.get('data_paths.backup_data')
            
            Path(backup_dir).mkdir(parents=True, exist_ok=True)
            
            source_path = Path(source_file)
            if not source_path.exists():
                raise FileNotFoundError(f"源文件不存在: {source_file}")
            
            # 生成备份文件名
            timestamp = datetime.now().strftime(
                self.config.get('data_processing.timestamp_format', '%Y%m%d_%H%M%S'))
            backup_filename = f"{timestamp}_{source_path.name}"
            backup_path = Path(backup_dir) / backup_filename
            
            # 执行备份
            shutil.copy2(source_file, backup_path)
            self.logger.info(f"文件备份成功: {source_file} -> {backup_path}")
            
            return str(backup_path)
            
        except Exception as e:
            self.logger.error(f"文件备份失败: {e}")
            raise


class DataValidator:
    """
    数据验证器
    
    提供各种数据格式的验证功能，包括：
    - 手机号格式验证（支持中国大陆手机号）
    - 金额有效性验证（正数且大于最小值）
    - 日期格式验证和标准化（支持多种输入格式）
    
    验证规则通过配置文件定义，便于维护和修改。
    所有验证方法都具有良好的异常处理能力。
    
    设计特点：
    - 配置驱动：验证规则通过配置文件定义
    - 容错性强：对各种异常输入都有适当处理
    - 标准化输出：统一的数据格式输出
    """
    
    def __init__(self, config_manager: ConfigManager):
        """
        初始化数据验证器
        
        Args:
            config_manager: 配置管理器实例，用于获取验证规则配置
            
        初始化过程：
        1. 保存配置管理器引用
        2. 设置专用的日志记录器
        """
        self.config = config_manager  # 保存配置管理器引用
        # 为数据验证器设置专用日志记录器
        self.logger = LoggerSetup.setup_logger('DataValidator', 
                                             config_manager.get('logging', {}))
    
    def validate_phone_number(self, phone: str) -> bool:
        """
        验证手机号格式
        
        验证中国大陆手机号格式，支持：
        - 11位数字
        - 以1开头
        - 第二位为3-9的数字
        
        Args:
            phone: 手机号字符串
            
        Returns:
            bool: True表示格式正确，False表示格式错误
            
        验证规则：
        - 必须是字符串类型
        - 不能为空值或NaN
        - 符合正则表达式模式（从配置文件获取）
        
        示例：
            validate_phone_number("13812345678")  # True
            validate_phone_number("1234567890")   # False
        """
        # 检查输入是否为空值或非字符串类型
        if pd.isna(phone) or not isinstance(phone, str):
            return False
        
        # 从配置文件获取手机号验证的正则表达式模式
        pattern = self.config.get('data_cleaning.validation_rules.phone_pattern')
        # 使用正则表达式验证手机号格式（去除前后空格）
        return bool(re.match(pattern, phone.strip()))
    
    def validate_amount(self, amount: Any) -> bool:
        """
        验证金额有效性
        
        验证金额是否为有效的正数，要求：
        - 能够转换为浮点数
        - 大于等于配置的最小金额
        
        Args:
            amount: 金额值，可以是数字、字符串等任意类型
            
        Returns:
            bool: True表示金额有效，False表示金额无效
            
        验证规则：
        - 必须能转换为数字
        - 必须大于等于最小金额（从配置获取，默认0.01）
        
        示例：
            validate_amount(100)      # True
            validate_amount("50.5")   # True
            validate_amount(-10)      # False
            validate_amount("abc")    # False
        """
        try:
            # 尝试将输入转换为浮点数
            amount_float = float(amount)
            # 从配置获取最小金额，默认为0.01
            min_amount = self.config.get('data_cleaning.validation_rules.amount_min', 0.01)
            # 检查金额是否大于等于最小值
            return amount_float >= min_amount
        except (ValueError, TypeError):
            # 如果转换失败或类型错误，返回False
            return False
    
    def validate_date_format(self, date_str: str) -> Tuple[bool, Optional[str]]:
        """
        验证并转换日期格式
        
        支持多种输入日期格式，并统一转换为YYYY-MM-DD格式。
        支持的格式通过配置文件定义，常见格式包括：
        - YYYY/MM/DD, YYYY-MM-DD
        - YYYY/M/D, YYYY-M-D
        - MM/DD/YYYY, M/D/YYYY
        
        Args:
            date_str: 日期字符串
            
        Returns:
            Tuple[bool, Optional[str]]: 
                - 第一个元素：是否为有效日期格式
                - 第二个元素：标准化后的日期字符串（YYYY-MM-DD）或None
                
        处理逻辑：
        1. 检查输入有效性
        2. 遍历配置的日期格式列表
        3. 尝试解析每种格式
        4. 成功解析后转换为标准格式
        
        示例：
            validate_date_format("2024/01/15")  # (True, "2024-01-15")
            validate_date_format("01/15/2024")  # (True, "2024-01-15")
            validate_date_format("invalid")     # (False, None)
        """
        # 检查输入是否为空值或非字符串类型
        if pd.isna(date_str) or not isinstance(date_str, str):
            return False, None
        
        # 从配置获取支持的输入日期格式列表
        input_formats = self.config.get('data_cleaning.date_formats.input_formats', [])
        
        # 遍历每种支持的日期格式
        for fmt in input_formats:
            try:
                # 将配置格式转换为Python datetime格式
                # YYYY -> %Y, MM -> %m, DD -> %d
                python_fmt = fmt.replace('YYYY', '%Y').replace('MM', '%m').replace('DD', '%d')
                # 处理单位数的月份和日期：M -> %m, D -> %d
                python_fmt = python_fmt.replace('M', '%m').replace('D', '%d')
                
                # 尝试使用当前格式解析日期字符串
                parsed_date = datetime.strptime(date_str.strip(), python_fmt)
                # 解析成功，返回标准格式的日期字符串
                return True, parsed_date.strftime('%Y-%m-%d')
            except ValueError:
                # 当前格式解析失败，继续尝试下一种格式
                continue
        
        # 所有格式都解析失败，返回失败结果
        return False, None


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, config_manager: ConfigManager):
        """
        初始化性能监控器
        
        Args:
            config_manager: 配置管理器实例
        """
        self.config = config_manager
        self.logger = LoggerSetup.setup_logger('PerformanceMonitor', 
                                             config_manager.get('logging', {}))
        self.start_time = None
        self.checkpoints = {}
    
    def start_monitoring(self, operation_name: str) -> None:
        """
        开始监控操作
        
        Args:
            operation_name: 操作名称
        """
        self.start_time = datetime.now()
        self.logger.info(f"开始监控操作: {operation_name}")
    
    def checkpoint(self, checkpoint_name: str) -> None:
        """
        设置检查点
        
        Args:
            checkpoint_name: 检查点名称
        """
        if self.start_time:
            elapsed = (datetime.now() - self.start_time).total_seconds()
            self.checkpoints[checkpoint_name] = elapsed
            self.logger.info(f"检查点 {checkpoint_name}: {elapsed:.2f}秒")
    
    def end_monitoring(self, operation_name: str) -> float:
        """
        结束监控操作
        
        Args:
            operation_name: 操作名称
            
        Returns:
            总耗时（秒）
        """
        if self.start_time:
            total_time = (datetime.now() - self.start_time).total_seconds()
            self.logger.info(f"操作 {operation_name} 完成，总耗时: {total_time:.2f}秒")
            return total_time
        return 0.0
    
    def get_performance_report(self) -> Dict[str, float]:
        """
        获取性能报告
        
        Returns:
            性能数据字典
        """
        return self.checkpoints.copy()


# TODO: 扩展点 - 可以添加更多工具类
# 1. 缓存管理器 (CacheManager)
# 2. 数据库连接管理器 (DatabaseManager) 
# 3. 邮件通知器 (EmailNotifier)
# 4. 报告生成器 (ReportGenerator)
# 5. 数据加密器 (DataEncryptor)
