"""
金豆发放申请校验系统 - 主程序
整合各个模块，提供完整的校验流程

本模块是系统的核心控制器，负责：
1. 协调各个处理阶段的执行顺序
2. 管理系统状态和结果收集
3. 处理用户交互和异常情况
4. 生成最终的处理报告

主要类：
- ValidationResult: 校验结果数据类
- GoldBeanValidationSystem: 主校验系统类

"""

# 导入标准库模块
import pandas as pd  # 数据处理核心库
import sys  # 系统相关功能
from pathlib import Path  # 路径操作
from typing import Dict, List, Tuple, Any, Optional  # 类型注解
import logging  # 日志记录
from datetime import datetime  # 日期时间处理

# 添加src目录到Python路径，确保能够导入自定义模块
sys.path.append(str(Path(__file__).parent))

# 导入自定义模块
from utils import ConfigManager, LoggerSetup, PerformanceMonitor  # 工具类模块
from excel_converter import ExcelToCSVConverter  # Excel转换模块
from data_cleaning import DataCleaner  # 数据清洗模块
from duplicate_conflict_detector import DuplicateConflictDetector  # 重复冲突检测模块
from limit_rule_engine import LimitRuleEngine  # 限额规则引擎模块
from data_processor import DataProcessor  # 数据处理模块


class ValidationResult:
    """
    校验结果数据类
    
    用于存储整个校验流程的执行结果，包括：
    - 执行状态和阶段信息
    - 各类问题的检测结果
    - 处理统计信息
    - 时间和性能数据
    """
    
    def __init__(self):
        """
        初始化校验结果对象
        
        设置所有结果字段的初始值，记录开始时间
        """
        self.success = False  # 校验是否成功完成
        self.stage = "初始化"  # 当前处理阶段
        self.processed_files = []  # 已处理的文件列表
        self.quality_issues = []  # 数据质量问题列表
        self.duplicate_records = []  # 重复数据记录列表
        self.conflict_records = []  # 冲突数据记录列表
        self.limit_violations = []  # 限额违规记录列表
        self.processing_result = None  # 数据处理结果对象
        self.summary = {}  # 处理摘要信息字典
        self.recommendations = []  # 建议操作列表
        self.start_time = datetime.now()  # 校验开始时间
        self.end_time = None  # 校验结束时间
        self.total_time = 0  # 总耗时（秒）
    
    def mark_completed(self, success: bool = True) -> None:
        """
        标记校验流程完成
        
        Args:
            success: 是否成功完成，默认为True
            
        功能：
        1. 设置成功状态
        2. 记录结束时间
        3. 计算总耗时
        """
        self.success = success  # 设置成功状态
        self.end_time = datetime.now()  # 记录结束时间
        # 计算总耗时（秒）
        self.total_time = (self.end_time - self.start_time).total_seconds()
    
    def to_dict(self) -> Dict[str, Any]:
        """
        将校验结果转换为字典格式
        
        Returns:
            包含所有校验结果信息的字典
            
        用途：
        - 生成报告时使用
        - 序列化结果数据
        - 日志记录
        """
        return {
            '校验状态': '成功' if self.success else '失败',  # 校验状态文本
            '当前阶段': self.stage,  # 当前或最后处理阶段
            '处理文件数': len(self.processed_files),  # 处理的文件数量
            '数据质量问题数': len(self.quality_issues),  # 发现的质量问题数
            '重复数据组数': len(self.duplicate_records),  # 重复数据组数
            '冲突数据组数': len(self.conflict_records),  # 冲突数据组数
            '限额违规组数': len(self.limit_violations),  # 限额违规组数
            '总耗时(秒)': self.total_time,  # 总处理时间
            # 格式化开始时间，如果存在的话
            '开始时间': self.start_time.strftime('%Y-%m-%d %H:%M:%S') if self.start_time else None,
            # 格式化结束时间，如果存在的话
            '结束时间': self.end_time.strftime('%Y-%m-%d %H:%M:%S') if self.end_time else None,
            '摘要信息': self.summary,  # 详细摘要信息
            '建议操作': self.recommendations  # 建议的后续操作
        }


class GoldBeanValidationSystem:
    """
    金豆发放申请校验系统主控制器
    
    这是系统的核心类，负责：
    1. 初始化所有子模块
    2. 协调整个校验流程的执行
    3. 管理系统状态和异常处理
    4. 提供用户交互接口
    
    系统处理流程：
    Excel转换 -> 数据清洗 -> 重复冲突检测 -> 限额校验 -> 数据更新
    """
    
    def __init__(self, config_path: str = "config.yaml"):
        """
        初始化校验系统
        
        Args:
            config_path: 配置文件路径，默认为"config.yaml"
            
        初始化步骤：
        1. 加载配置文件
        2. 设置日志系统
        3. 初始化性能监控
        4. 创建各个功能模块实例
        5. 创建结果对象
        
        Raises:
            Exception: 当初始化失败时抛出异常
        """
        try:
            # 1. 加载配置管理器
            self.config = ConfigManager(config_path)
            
            # 2. 设置日志记录器，使用系统名称作为日志器名称
            self.logger = LoggerSetup.setup_logger('GoldBeanValidationSystem', 
                                                 self.config.get('logging', {}))
            
            # 3. 初始化性能监控器
            self.monitor = PerformanceMonitor(self.config)
            
            # 4. 初始化各个功能模块
            self.excel_converter = ExcelToCSVConverter(config_path)  # Excel转CSV转换器
            self.data_cleaner = DataCleaner(self.config)  # 数据清洗器
            self.duplicate_detector = DuplicateConflictDetector(self.config)  # 重复冲突检测器
            self.limit_engine = LimitRuleEngine(self.config)  # 限额规则引擎
            self.data_processor = DataProcessor(self.config)  # 数据处理器
            
            # 5. 创建校验结果对象
            self.result = ValidationResult()
            
            # 记录初始化成功日志
            self.logger.info("金豆发放申请校验系统初始化完成")
            
        except Exception as e:
            # 初始化失败时，先打印错误信息，然后重新抛出异常
            print(f"系统初始化失败: {e}")
            raise
    
    # run_validation_process方法返回一个ValidationResult类型的对象
    def run_validation_process(self) -> ValidationResult:
        """
        运行完整的校验流程
        
        这是系统的主要入口方法，按顺序执行所有校验阶段：
        1. Excel到CSV转换
        2. 数据清洗与转换
        3. 重复和冲突检测
        4. 限额规则校验
        5. 数据处理与更新
        
        Returns:
            ValidationResult: 包含完整校验结果的对象
            
        处理逻辑：
        - 每个阶段都有独立的错误处理
        - 发现问题时会停止后续处理
        - 用户可以选择如何处理某些问题
        - 所有结果都会记录在result对象中
        """
        try:
            # 开始性能监控
            self.monitor.start_monitoring("完整校验流程")
            self.logger.info("开始执行金豆发放申请校验流程")
            
            # ========== 阶段0: Excel到CSV转换 ==========
            self.result.stage = "Excel到CSV转换"  # 更新当前阶段
            conversion_result = self._stage_excel_conversion()  # 执行Excel转换
            
            # 检查转换是否成功
            if not conversion_result['success']:
                self.result.stage = "Excel转换失败"  # 更新失败阶段
                self.result.mark_completed(False)  # 标记为失败完成
                self._generate_final_summary()  # 生成最终摘要
                return self.result  # 返回失败结果
            
            # 记录阶段完成的性能检查点
            self.monitor.checkpoint("Excel转换完成")
            
            # ========== 阶段1: 数据清洗与转换 ==========
            self.result.stage = "数据清洗与转换"  # 更新当前阶段
            temp_df, quality_issues = self._stage_data_cleaning()  # 执行数据清洗，返回清洗后的数据框和质量问题列表
            
            # 检查是否有数据质量问题
            if quality_issues:
                self.result.quality_issues = quality_issues  # 保存质量问题
                self.result.stage = "数据质量问题"  # 更新问题阶段
                self.result.mark_completed(False)  # 标记为失败完成
                self._generate_final_summary()  # 生成最终摘要
                return self.result  # 返回失败结果
            
            # 记录阶段完成的性能检查点
            self.monitor.checkpoint("数据清洗完成")
            
            # ========== 阶段2: 重复和冲突检测 ==========
            self.result.stage = "重复和冲突检测"  # 更新当前阶段
            # 执行重复和冲突检测，返回检测到的问题列表
            duplicate_records, conflict_records = self._stage_duplicate_conflict_detection(temp_df)
            
            # 保存检测结果到result对象
            self.result.duplicate_records = duplicate_records
            self.result.conflict_records = conflict_records
            
            # 检查是否需要停止处理（根据检测结果和用户选择）
            should_stop, user_choice = self._check_stop_conditions(duplicate_records, conflict_records)
            
            # 根据用户选择决定是否继续处理
            if should_stop:
                if user_choice == "查看报告":
                    self.result.stage = "用户选择查看报告"  # 更新阶段状态
                    self.result.mark_completed(False)  # 标记为未成功完成
                    self._generate_final_summary()  # 生成最终摘要
                    return self.result  # 返回结果
                elif user_choice == "退出系统":
                    self.result.stage = "用户选择退出系统"  # 更新阶段状态
                    self.result.mark_completed(False)  # 标记为未成功完成
                    self._generate_final_summary()  # 生成最终摘要
                    return self.result  # 返回结果
                else:
                    self.result.stage = "用户选择停止处理"  # 更新阶段状态
                    self.result.mark_completed(False)  # 标记为未成功完成
                    self._generate_final_summary()  # 生成最终摘要
                    return self.result  # 返回结果
            
            # 记录阶段完成的性能检查点
            self.monitor.checkpoint("重复冲突检测完成")
            
            # ========== 阶段3: 限额规则校验 ==========
            self.result.stage = "限额规则校验"  # 更新当前阶段
            # 执行限额规则校验，检查是否违反单次、年累计、户累计限额
            limit_violations = self._stage_limit_validation(temp_df)
            
            # 保存限额违规结果
            self.result.limit_violations = limit_violations
            
            # 如果发现限额违规，停止处理
            if limit_violations:
                self.result.stage = "限额规则违规"  # 更新违规阶段
                self.result.mark_completed(False)  # 标记为失败完成
                self._generate_final_summary()  # 生成最终摘要
                return self.result  # 返回失败结果
            
            # 记录阶段完成的性能检查点
            self.monitor.checkpoint("限额规则校验完成")
            
            # ========== 阶段4: 数据处理与更新 ==========
            self.result.stage = "数据处理与更新"  # 更新当前阶段
            # 执行数据处理，包括备份、主表更新、统计表更新
            processing_result = self._stage_data_processing(temp_df)
            
            # 保存处理结果
            self.result.processing_result = processing_result
            
            # 检查数据处理是否成功
            if not processing_result.success:
                self.result.stage = "数据处理失败"  # 更新失败阶段
                self.result.mark_completed(False)  # 标记为失败完成
                self._generate_final_summary()  # 生成最终摘要
                return self.result  # 返回失败结果
            
            # 记录阶段完成的性能检查点
            self.monitor.checkpoint("数据处理完成")
            
            # ========== 校验流程成功完成 ==========
            self.result.stage = "校验完成"  # 更新完成阶段
            self.result.mark_completed(True)  # 标记为成功完成
            self._generate_final_summary()  # 生成最终摘要
            
            # 结束性能监控
            self.monitor.end_monitoring("完整校验流程")
            
            # 记录成功完成日志
            self.logger.info("金豆发放申请校验流程成功完成")
            return self.result  # 返回成功结果
            
        except Exception as e:
            # 捕获所有未预期的异常
            self.logger.error(f"校验流程执行失败: {e}")  # 记录错误日志
            self.result.stage = f"系统错误: {str(e)}"  # 更新错误阶段信息
            self.result.mark_completed(False)  # 标记为失败完成
            self._generate_final_summary()  # 生成最终摘要
            return self.result  # 返回失败结果
    
    def _stage_excel_conversion(self) -> Dict[str, Any]:
        """
        Excel到CSV转换阶段
        
        功能：
        1. 查找Raw_xlsx目录中的Excel文件
        2. 将Excel文件转换为CSV格式
        3. 保存到Raw_csv目录
        4. 统计转换结果
        
        Returns:
            Dict[str, Any]: 转换结果字典，包含：
                - success: 是否成功
                - stats: 转换统计信息
                - error: 错误信息（如果有）
        """
        try:
            # 记录阶段开始日志
            self.logger.info("开始Excel到CSV转换阶段")
            
            # 调用Excel转换器执行批量转换
            conversion_stats = self.excel_converter.convert_excel_files()
            
            # 判断转换是否成功：有成功转换且无失败转换
            success = (conversion_stats['successful_conversions'] > 0 and 
                      conversion_stats['failed_conversions'] == 0)
            
            # 将转换统计信息保存到结果摘要中
            self.result.summary['excel_conversion'] = conversion_stats
            
            # 根据转换结果记录相应的日志
            if success:
                self.logger.info(f"Excel转换成功完成，共转换 {conversion_stats['successful_conversions']} 个文件")
            else:
                self.logger.warning(f"Excel转换存在问题，成功: {conversion_stats['successful_conversions']}, "
                                  f"失败: {conversion_stats['failed_conversions']}")
            
            # 返回转换结果
            return {
                'success': success,  # 转换是否成功
                'stats': conversion_stats  # 详细统计信息
            }
            
        except Exception as e:
            # 捕获转换过程中的异常
            self.logger.error(f"Excel转换阶段发生错误: {e}")
            return {
                'success': False,  # 标记为失败
                'error': str(e)  # 保存错误信息
            }
    
    def _stage_data_cleaning(self) -> Tuple[pd.DataFrame, List]:
        """
        数据清洗阶段
        
        Returns:
            (清洗后的数据框, 数据质量问题列表)
        """
        try:
            self.logger.info("开始数据清洗阶段")
            
            # 查找所有申请明细表文件
            from utils import FileManager
            file_manager = FileManager(self.config)
            detail_files = file_manager.find_detail_tables()
            
            if not detail_files:
                raise Exception("未找到任何申请明细表文件")
            
            self.result.processed_files = detail_files
            
            # 清洗多个文件
            temp_df, all_issues = self.data_cleaner.clean_multiple_files(detail_files)
            
            if all_issues:
                # 生成数据质量报告
                quality_report = self.data_cleaner.generate_quality_report(all_issues)
                self._save_report(quality_report, "数据质量问题报告")
                
                self.logger.error(f"发现 {len(all_issues)} 个数据质量问题，处理终止")
                return temp_df, all_issues
            
            if temp_df.empty:
                raise Exception("清洗后没有有效数据")
            
            # 保存临时汇总表
            temp_file_path = self.data_cleaner.save_temp_summary_table(temp_df)
            self.logger.info(f"临时汇总表保存成功: {temp_file_path}")
            
            return temp_df, []
            
        except Exception as e:
            self.logger.error(f"数据清洗阶段失败: {e}")
            raise
    
    def _stage_duplicate_conflict_detection(self, temp_df: pd.DataFrame) -> Tuple[List, List]:
        """
        重复和冲突检测阶段
        
        Args:
            temp_df: 临时汇总表数据
            
        Returns:
            (重复记录列表, 冲突记录列表)
        """
        try:
            self.logger.info("开始重复和冲突检测阶段")
            
            # 读取主明细表用于检测
            raw_data_path = self.config.get('data_paths.raw_data')
            main_table_file = Path(raw_data_path) / self.config.get('table_files.main_detail_table')
            
            # 指定需要保持为字符串类型的列
            dtype_dict = {
                '客户手机号': str,
                '客户名称': str,
                '客户归属企业': str,
                '申请发放机构': str,
                '活动方案全称': str,
                '归属条线': str,
                '备注（非必填）': str
            }
            
            main_df = None
            if main_table_file.exists():
                main_df = pd.read_csv(main_table_file, encoding='utf-8-sig', dtype=dtype_dict)
                self.logger.info(f"读取主明细表: {len(main_df)} 条记录")
            
            # 执行检测
            duplicate_records, conflict_records = self.duplicate_detector.detect_duplicates_and_conflicts(
                temp_df, main_df
            )
            
            # 生成检测报告
            if duplicate_records:
                duplicate_report = self.duplicate_detector.generate_duplicate_report(duplicate_records)
                self._save_report(duplicate_report, "重复数据报告")
            
            if conflict_records:
                conflict_report = self.duplicate_detector.generate_conflict_report(conflict_records)
                self._save_report(conflict_report, "冲突数据报告")
            
            return duplicate_records, conflict_records
            
        except Exception as e:
            self.logger.error(f"重复和冲突检测阶段失败: {e}")
            raise

    def _check_stop_conditions(self, duplicate_records: List, conflict_records: List) -> Tuple[bool, str]:
        """
        检查是否需要停止处理

        Args:
            duplicate_records: 重复记录列表
            conflict_records: 冲突记录列表

        Returns:
            (是否停止, 用户选择)
        """
        try:
            if not duplicate_records and not conflict_records:
                return False, "继续处理"

            # 显示检测结果摘要
            print("\n" + "="*60)
            print("重复和冲突检测结果摘要")
            print("="*60)

            if duplicate_records:
                print(f"发现重复数据: {len(duplicate_records)} 组")
                for i, dup in enumerate(duplicate_records[:3], 1):  # 显示前3组
                    key_info = str(dup.key_values) if dup.key_values else "未知字段"
                    print(f"  {i}. 重复字段: {key_info}, 记录数: {len(dup.records)} 条")
                if len(duplicate_records) > 3:
                    print(f"  ... 还有 {len(duplicate_records) - 3} 组重复数据")

            if conflict_records:
                print(f"发现冲突数据: {len(conflict_records)} 组")
                for i, conf in enumerate(conflict_records[:3], 1):  # 显示前3组
                    print(f"  {i}. {conf.conflict_type}: {len(conf.conflicting_records)} 条记录")
                if len(conflict_records) > 3:
                    print(f"  ... 还有 {len(conflict_records) - 3} 组冲突数据")

            # 根据检测结果提供不同的处理选项
            if duplicate_records and conflict_records:
                # 同时存在重复和冲突：阻断后续流程
                print("\n⚠️  检测到重复数据和冲突数据，系统将阻断后续流程")
                print("请先解决重复和冲突问题后再继续处理")
                print("\n请选择操作:")
                print("1. 查看详细报告")
                print("2. 退出系统")
                
                while True:
                    try:
                        choice = input("\n请输入选择 (1/2): ").strip()
                        
                        if choice == "1":
                            self.logger.info("用户选择查看详细报告")
                            return True, "查看报告"
                        elif choice == "2":
                            self.logger.info("用户选择退出系统")
                            return True, "退出系统"
                        else:
                            print("无效选择，请输入 1 或 2")
                            
                    except KeyboardInterrupt:
                        print("\n用户中断操作")
                        return True, "用户中断"
                        
            elif duplicate_records:
                # 仅存在重复：阻断后续流程
                print("\n⚠️  检测到重复数据，系统将阻断后续流程")
                print("请先解决重复问题后再继续处理")
                print("\n请选择操作:")
                print("1. 查看详细报告")
                print("2. 退出系统")
                
                while True:
                    try:
                        choice = input("\n请输入选择 (1/2): ").strip()
                        
                        if choice == "1":
                            self.logger.info("用户选择查看详细报告")
                            return True, "查看报告"
                        elif choice == "2":
                            self.logger.info("用户选择退出系统")
                            return True, "退出系统"
                        else:
                            print("无效选择，请输入 1 或 2")
                            
                    except KeyboardInterrupt:
                        print("\n用户中断操作")
                        return True, "用户中断"
                        
            elif conflict_records:
                # 仅存在冲突：提供用户选择是否继续
                print("\n⚠️  检测到冲突数据，但不影响后续处理")
                print("您可以选择继续处理或先解决冲突问题")
                print("\n请选择处理方式:")
                print("1. 继续处理（忽略冲突，执行后续限额规则校验）")
                print("2. 停止处理，查看详细报告")
                print("3. 退出系统")
                
                while True:
                    try:
                        choice = input("\n请输入选择 (1/2/3): ").strip()
                        
                        if choice == "1":
                            self.logger.info("用户选择继续处理，忽略冲突")
                            return False, "继续处理"
                        elif choice == "2":
                            self.logger.info("用户选择停止处理，查看详细报告")
                            return True, "查看报告"
                        elif choice == "3":
                            self.logger.info("用户选择退出系统")
                            return True, "退出系统"
                        else:
                            print("无效选择，请输入 1、2 或 3")
                            
                    except KeyboardInterrupt:
                        print("\n用户中断操作")
                        return True, "用户中断"

        except Exception as e:
            self.logger.error(f"检查停止条件时发生错误: {e}")
            return True, "系统错误"

    def _stage_limit_validation(self, temp_df: pd.DataFrame) -> List:
        """
        限额规则校验阶段

        Args:
            temp_df: 临时汇总表数据

        Returns:
            限额违规记录列表
        """
        try:
            self.logger.info("开始限额规则校验阶段")

            # 读取主明细表用于校验
            raw_data_path = self.config.get('data_paths.raw_data')
            main_table_file = Path(raw_data_path) / self.config.get('table_files.main_detail_table')
            
            # 指定需要保持为字符串类型的列
            dtype_dict = {
                '客户手机号': str,
                '客户名称': str,
                '客户归属企业': str,
                '申请发放机构': str,
                '活动方案全称': str,
                '归属条线': str,
                '备注（非必填）': str
            }
            
            main_df = None
            if main_table_file.exists():
                main_df = pd.read_csv(main_table_file, encoding='utf-8-sig', dtype=dtype_dict)
                self.logger.info(f"读取主明细表用于限额校验: {len(main_df)} 条记录")

            # 执行限额校验，返回违规记录列表和是否通过校验
            violations, is_passed = self.limit_engine.validate_limits(temp_df, main_df)

            if violations:
                # 生成违规报告
                violation_report = self.limit_engine.generate_violation_report(violations)
                self._save_report(violation_report, "限额违规报告")

                # 生成违规摘要
                violation_summary = self.limit_engine.get_violation_summary(violations)
                self.logger.warning(f"发现限额违规: {violation_summary}")

                # 显示违规摘要
                print("\n" + "="*60)
                print("限额规则违规摘要")
                print("="*60)
                print(f"总违规组数: {violation_summary.get('总违规组数', 0)}")
                print(f"总违规记录数: {violation_summary.get('总违规记录数', 0)}")

                for rule_code, stats in violation_summary.get('按规则统计', {}).items():
                    print(f"\n{rule_code}:")
                    print(f"  违规组数: {stats.get('违规组数', 0)}")
                    print(f"  涉及客户数: {stats.get('涉及客户数', 0)}")
                    print(f"  总超限金额: {stats.get('总超限金额', 0)}")

            return violations

        except Exception as e:
            self.logger.error(f"限额规则校验阶段失败: {e}")
            raise

    def _stage_data_processing(self, temp_df: pd.DataFrame):
        """
        数据处理与更新阶段

        Args:
            temp_df: 临时汇总表数据

        Returns:
            处理结果
        """
        try:
            self.logger.info("开始数据处理与更新阶段")

            # 执行数据处理
            processing_result = self.data_processor.process_validated_data(temp_df)

            # 生成处理报告
            processing_report = self.data_processor.generate_processing_report()
            self._save_report(processing_report, "数据处理报告")

            return processing_result

        except Exception as e:
            self.logger.error(f"数据处理与更新阶段失败: {e}")
            raise

    def _save_report(self, report_data: Any, report_name: str) -> None:
        """
        保存报告

        Args:
            report_data: 报告数据
            report_name: 报告名称
        """
        try:
            output_path = self.config.get('data_paths.reports')
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            if isinstance(report_data, pd.DataFrame):
                # 保存为CSV文件
                report_file = Path(output_path) / f"{timestamp}_{report_name}.csv"
                report_data.to_csv(report_file, index=False, encoding='utf-8-sig')
            else:
                # 保存为文本文件
                report_file = Path(output_path) / f"{timestamp}_{report_name}.txt"
                with open(report_file, 'w', encoding='utf-8') as f:
                    if isinstance(report_data, dict):
                        import json
                        f.write(json.dumps(report_data, ensure_ascii=False, indent=2))
                    else:
                        f.write(str(report_data))

            self.logger.info(f"报告保存成功: {report_file}")

        except Exception as e:
            self.logger.error(f"保存报告时发生错误: {e}")

    def _generate_final_summary(self) -> None:
        """生成最终摘要"""
        try:
            summary = {
                '校验状态': '成功' if self.result.success else '失败',
                '处理阶段': self.result.stage,
                '处理文件': len(self.result.processed_files),
                '数据质量问题': len(self.result.quality_issues),
                '重复数据组': len(self.result.duplicate_records),
                '冲突数据组': len(self.result.conflict_records),
                '限额违规组': len(self.result.limit_violations),
                '总耗时': f"{self.result.total_time:.2f}秒"
            }

            if self.result.processing_result:
                summary.update({
                    '处理记录数': self.result.processing_result.processed_records,
                    '新增客户数': self.result.processing_result.new_customers,
                    '更新客户数': self.result.processing_result.updated_customers
                })

            self.result.summary = summary

            # 生成建议
            recommendations = []

            if self.result.success:
                recommendations.append("校验流程成功完成，数据已更新到主明细表和统计表")
            else:
                recommendations.append(f"校验流程在'{self.result.stage}'阶段停止")

                if self.result.quality_issues:
                    recommendations.append("请检查数据质量问题报告，修复数据后重新处理")

                if self.result.duplicate_records or self.result.conflict_records:
                    recommendations.append("请检查重复和冲突数据报告，确认数据准确性")

                if self.result.limit_violations:
                    recommendations.append("请检查限额违规报告，确认是否需要调整限额规则")

            # 添加性能建议
            performance_report = self.monitor.get_performance_report()
            if performance_report.get('总耗时(秒)', 0) > 60:
                recommendations.append("处理时间较长，建议优化数据文件大小或系统配置")

            self.result.recommendations = recommendations

        except Exception as e:
            self.logger.error(f"生成最终摘要时发生错误: {e}")

    def print_final_result(self) -> None:
        """打印最终结果"""
        try:
            print("\n" + "="*80)
            print("金豆发放申请校验系统 - 最终结果")
            print("="*80)

            for key, value in self.result.summary.items():
                print(f"{key}: {value}")

            if self.result.recommendations:
                print("\n建议操作:")
                for i, rec in enumerate(self.result.recommendations, 1):
                    print(f"{i}. {rec}")

            print("\n" + "="*80)

        except Exception as e:
            self.logger.error(f"打印最终结果时发生错误: {e}")


def main():
    """主函数"""
    try:
        print("金豆发放申请校验系统启动中...")

        # 初始化系统
        system = GoldBeanValidationSystem()

        # 运行校验流程
        result = system.run_validation_process()

        # 打印最终结果
        system.print_final_result()

        # 返回退出码
        return 0 if result.success else 1

    except Exception as e:
        print(f"系统运行失败: {e}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
