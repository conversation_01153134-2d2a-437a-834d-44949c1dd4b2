# 金豆发放申请校验系统配置文件

# 数据路径配置
data_paths:
  raw_excel: "Data/Raw_xlsx/"     # Excel文件目录
  raw_csv: "Data/Raw_csv/"        # CSV文件目录
  raw_data: "Data/Raw/"       # 原始数据路径（指向CSV目录）
  backup_data: "Data/Backup/"
  processed_data: "Data/Processed/"
  reports: "Reports/"
  logs: "logs/"

# 数据表文件名配置
table_files:
  # 主要数据表
  main_detail_table: "金豆发放申请主明细表.csv"
  temp_summary_table: "金豆发放申请明细临时汇总表.csv"
  customer_stats_table: "金豆发放户统计表.csv"
  yearly_stats_table: "金豆发放年统计表.csv"
  rules_config_table: "金豆发放规则配置表.csv"
  
  # 申请明细表匹配模式
  detail_table_pattern: "金豆发放申请明细表"

# 数据清洗配置
data_cleaning:
  # 日期格式配置
  date_formats:
    input_formats: ["YYYY/MM/DD", "YYYY-MM-DD", "YYYY/M/D", "YYYY-M-D", "MM/DD/YYYY", "M/D/YYYY", "MM/DD/YY", "M/D/YY"]
    output_format: "YYYY-MM-DD"
  
  # 字段清理配置
  string_fields_to_clean:
    - "客户名称"
    - "客户手机号"
    - "客户归属企业"
    - "申请发放机构"
  
  # 数据验证规则
  validation_rules:
    phone_pattern: "^1[3-9]\\d{9}$"
    amount_min: 0.01
    required_fields:
      - "客户名称"
      - "客户手机号"
      - "等值金豆发放数量"
      - "申请发放机构"
      - "申请发放日期"
      - "申请填报日期"
      - "有效时间（天）"
      - "活动方案全称"
      - "归属条线"

    # 条件必填字段
    conditional_required:
      - field: "客户归属企业"
        condition: "归属条线 == '公司'"

# 重复和冲突检测配置
duplicate_detection:
  # 重复判定字段组合
  duplicate_key_fields:
    - "客户名称"
    - "客户手机号"
    - "申请发放日期"
    - "客户归属企业"
    - "申请发放机构"
    - "等值金豆发放数量"
  
  # 冲突检测配置
  conflict_detection:
    customer_key_fields:
      - "客户名称"
      - "客户手机号"
      - "客户归属企业"
    
    # 冲突类型
    conflict_types:
      name_conflict: "相同手机号+相同客户归属企业+不同客户名称"
      phone_conflict: "相同客户名称+相同客户归属企业+不同手机号"

# 限额规则配置
limit_rules:
  # 规则代码映射
  rule_codes:
    single_limit: "SINGLE_LIMIT"
    yearly_limit: "YEARLY_LIMIT"  # 注意：配置表中是YEAR_LIMIT，这里统一为YEARLY_LIMIT
    lifetime_limit: "CUSTOMER_TOTAL_LIMIT"  # 注意：配置表中是CUSTOMER_TOTAL_LIMIT，这里映射为lifetime_limit
  
  # 统计主键配置
  statistical_keys:
    single_limit:
      - "客户名称"
      - "客户手机号"
      - "客户归属企业"
      - "申请发放日期"
    
    yearly_limit:
      - "客户名称"
      - "客户手机号"
      - "年份"
      - "客户归属企业"
    
    lifetime_limit:
      - "客户名称"
      - "客户手机号"
      - "客户归属企业"

# 数据处理配置
data_processing:
  # 批处理配置
  batch_size: 1000
  
  # 备份文件名格式
  backup_filename_format: "{timestamp}_{table_name}"
  timestamp_format: "%Y%m%d_%H%M%S"
  
  # 事务配置
  transaction_timeout: 300  # 秒
  max_retry_attempts: 3

# 性能优化配置
performance:
  # 大数据量阈值
  large_dataset_threshold: 10000
  
  # 内存优化
  chunk_size: 5000
  
  # 并行处理
  enable_parallel: true
  max_workers: 4

# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_rotation: true
  max_file_size: "10MB"
  backup_count: 5

# 输出配置
output:
  # 报告格式
  report_formats: ["markdown", "csv"]
  
  # 详细程度
  detail_level: "full"  # full, summary, minimal
  
  # 导出选项
  export_options:
    include_charts: false
    include_raw_data: false
    compress_output: false

# 错误处理配置
error_handling:
  # 错误级别
  stop_on_data_quality_issues: true
  stop_on_duplicates: true
  stop_on_conflicts_only: false  # 仅冲突时允许用户选择继续
  stop_on_limit_violations: true
  
  # 重试配置
  retry_on_failure: true
  retry_delay: 1  # 秒
  
  # 回滚配置
  enable_rollback: true
  backup_before_processing: true
