#!/usr/bin/env python3
"""
金豆发放申请校验系统 - 运行脚本
提供简化的命令行接口
"""

import sys
import argparse
from pathlib import Path
from datetime import datetime

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.main import GoldBeanValidationSystem


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="金豆发放申请校验系统",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python run_validation.py                    # 使用默认配置运行
  python run_validation.py --config custom.yaml  # 使用自定义配置文件
  python run_validation.py --help             # 显示帮助信息

系统功能:
  1. 数据清洗与格式标准化
  2. 重复数据和冲突检测
  3. 限额规则校验
  4. 数据更新和统计表维护
  5. 详细报告生成

注意事项:
  - 确保配置文件中的路径设置正确
  - 运行前请备份重要数据
  - 处理大量数据时可能需要较长时间
        """
    )
    
    parser.add_argument(
        "--config", 
        default="config.yaml",
        help="配置文件路径 (默认: config.yaml)"
    )
    
    parser.add_argument(
        "--version",
        action="version",
        version="金豆发放申请校验系统 v1.0.0"
    )
    
    args = parser.parse_args()
    
    try:
        print("="*60)
        print("金豆发放申请校验系统")
        print("="*60)
        print(f"配置文件: {args.config}")
        # print(f"启动时间: {Path(__file__).stat().st_mtime}")
        print(f"启动时间: {datetime.fromtimestamp(Path(__file__).stat().st_mtime).strftime('%Y-%m-%d %H:%M:%S')}")
        print("-"*60)
        
        # 检查配置文件是否存在
        config_path = Path(args.config)
        if not config_path.exists():
            print(f"错误: 配置文件不存在 - {config_path}")
            print("请确保配置文件路径正确，或使用 --help 查看使用说明")
            return 1
        
        # 初始化并运行系统
        system = GoldBeanValidationSystem(str(config_path))
        result = system.run_validation_process()
        
        # 打印结果
        system.print_final_result()
        
        # 返回适当的退出码
        return 0 if result.success else 1
        
    except KeyboardInterrupt:
        print("\n用户中断操作，系统退出")
        return 130
    except Exception as e:
        print(f"\n系统运行失败: {e}")
        print("请检查配置文件和数据文件是否正确")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
