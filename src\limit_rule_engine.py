"""
金豆发放申请校验系统 - 限额规则校验引擎
负责执行各种限额规则的校验

本模块是业务规则控制的核心组件，主要功能包括：

核心功能：
1. 限额规则校验：执行单次、年累计、户累计三种限额规则
2. 规则配置管理：动态加载和解析规则配置表
3. 统计数据维护：更新客户统计表和年度统计表
4. 违规记录管理：详细记录和报告限额违规情况

限额规则类型：
1. 单次发放限制：同一客户同一日期的发放总额限制
2. 年累计发放限制：客户年度累计发放额度限制  
3. 户累计发放限制：客户历史累计发放额度限制

校验流程：
规则加载 -> 统计数据加载 -> 数据合并 -> 分组统计 -> 限额比较 -> 违规记录 -> 统计更新

设计特点：
- 配置驱动：规则通过配置表定义，支持动态调整
- 高效算法：使用分组聚合优化大数据量处理
- 完整审计：详细记录违规情况和处理过程
- 统计维护：自动更新相关统计表

数据依赖：
- 金豆发放规则配置表：定义各种限额规则和阈值
- 金豆发放户统计表：客户累计统计数据
- 金豆发放年统计表：客户年度统计数据

"""

# 导入标准库
import pandas as pd  # 数据处理核心库
import numpy as np   # 数值计算库
from typing import Dict, List, Tuple, Any, Optional  # 类型注解
from pathlib import Path  # 路径操作
import logging      # 日志记录
from datetime import datetime  # 日期时间处理
from collections import defaultdict  # 默认字典，用于统计

# 导入自定义工具类
from utils import ConfigManager, LoggerSetup, PerformanceMonitor


class LimitViolation:
    """
    限额违规记录类
    
    用于记录限额规则校验过程中发现的违规情况，包括：
    - 违规的具体规则信息
    - 客户关键识别信息
    - 当前金额与限制金额的对比
    - 涉及的所有违规记录详情
    - 违规发现的时间戳
    
    每个违规记录包含完整的上下文信息，便于：
    - 生成详细的违规报告
    - 进行违规原因分析
    - 支持后续的人工审核
    """
    
    def __init__(self, rule_code: str, rule_name: str, customer_key: Dict[str, Any],
                 current_amount: float, limit_amount: float, violation_records: List[Dict[str, Any]]):
        """
        初始化限额违规记录
        
        Args:
            rule_code: 规则代码（如SINGLE_LIMIT、YEARLY_LIMIT等）
            rule_name: 规则名称（如"单次发放限制"）
            customer_key: 客户关键信息字典（客户名称、手机号等）
            current_amount: 当前累计金额（实际发放金额）
            limit_amount: 规则限制金额（允许的最大金额）
            violation_records: 导致违规的具体记录列表
            
        计算字段：
        - excess_amount: 超限金额 = 当前金额 - 限制金额
        - record_count: 违规记录数量
        - timestamp: 违规发现时间
        """
        self.rule_code = rule_code          # 规则代码
        self.rule_name = rule_name          # 规则名称
        self.customer_key = customer_key    # 客户关键信息
        self.current_amount = current_amount # 当前累计金额
        self.limit_amount = limit_amount    # 限制金额
        self.violation_records = violation_records # 违规记录列表
        # 计算超限金额
        self.excess_amount = current_amount - limit_amount
        # 统计违规记录数量
        self.record_count = len(violation_records)
        # 记录发现时间
        self.timestamp = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典格式
        
        将违规记录转换为字典格式，便于：
        - 生成违规报告
        - 序列化为JSON或CSV
        - 在日志中记录
        - 数据库存储
        
        Returns:
            Dict[str, Any]: 包含所有违规信息的字典
            
        字典结构：
        - 基本信息：规则代码、名称、客户信息
        - 金额信息：当前金额、限制金额、超限金额
        - 统计信息：违规记录数、发现时间
        - 详细信息：完整的违规记录列表
        """
        return {
            '规则代码': self.rule_code,
            '规则名称': self.rule_name,
            '客户关键信息': str(self.customer_key),  # 转换为字符串便于显示
            '当前金额': self.current_amount,
            '限制金额': self.limit_amount,
            '超限金额': self.excess_amount,
            '违规记录数': self.record_count,
            # 格式化时间戳，如果存在的话
            '发现时间': self.timestamp.strftime('%Y-%m-%d %H:%M:%S') if self.timestamp else None,
            '详细记录': self.violation_records  # 完整的违规记录列表
        }


class LimitRuleEngine:
    """限额规则校验引擎"""
    
    def __init__(self, config_manager: ConfigManager):
        """
        初始化限额规则校验引擎
        
        Args:
            config_manager: 配置管理器实例
        """
        self.config = config_manager
        self.logger = LoggerSetup.setup_logger('LimitRuleEngine', 
                                             config_manager.get('logging', {}))
        self.monitor = PerformanceMonitor(config_manager)
        self.rules_config = None
        self.customer_stats = None
        self.yearly_stats = None
    
    def load_rules_and_stats(self) -> bool:
        """
        加载规则配置和统计数据
        
        Returns:
            是否加载成功
        """
        try:
            self.logger.info("开始加载规则配置和统计数据")
            
            # 加载规则配置
            self.rules_config = self._load_rules_config()
            if self.rules_config is None:
                return False
            
            # 加载统计数据
            self.customer_stats = self._load_customer_stats()
            self.yearly_stats = self._load_yearly_stats()
            
            self.logger.info("规则配置和统计数据加载完成")
            return True
            
        except Exception as e:
            self.logger.error(f"加载规则配置和统计数据时发生错误: {e}")
            return False
    
    def validate_limits(self, temp_df: pd.DataFrame, 
                       main_df: pd.DataFrame = None) -> Tuple[List[LimitViolation], bool]:
        """
        执行限额规则校验
        
        Args:
            temp_df: 临时汇总表数据
            main_df: 主明细表数据
            
        Returns:
            (违规记录列表, 是否通过校验)
        """
        try:
            self.monitor.start_monitoring("限额规则校验")
            
            if not self.load_rules_and_stats():
                raise Exception("无法加载规则配置和统计数据")
            
            all_violations = []
            
            # 合并数据用于校验，并添加数据来源标识
            if main_df is not None and not main_df.empty:
                # 为临时表数据添加来源标识
                temp_df_with_source = temp_df.copy()
                temp_df_with_source['数据来源'] = '新输入数据'
                temp_df_with_source['源文件名'] = temp_df_with_source.get('源文件名', '金豆发放申请明细表.csv')
                
                # 为主表数据添加来源标识
                main_df_with_source = main_df.copy()
                main_df_with_source['数据来源'] = '历史数据'
                main_df_with_source['源文件名'] = '金豆发放申请主明细表.csv'
                
                # 合并数据
                combined_df = pd.concat([temp_df_with_source, main_df_with_source], ignore_index=True)
                self.logger.info(f"合并临时表({len(temp_df)}条)和主表({len(main_df)}条)进行校验，已添加数据来源标识")
            else:
                combined_df = temp_df.copy()
                combined_df['数据来源'] = '新输入数据'
                combined_df['源文件名'] = combined_df.get('源文件名', '金豆发放申请明细表.csv')
                self.logger.info(f"仅对临时表({len(temp_df)}条)进行校验，已添加数据来源标识")
            
            self.monitor.checkpoint("数据合并完成")
            
            # 执行各种限额规则校验
            violations = []
            
            # 单次发放限制校验
            single_violations = self._validate_single_limit(combined_df, temp_df)
            violations.extend(single_violations)
            self.monitor.checkpoint("单次限额校验完成")
            
            # 年累计发放限制校验
            yearly_violations = self._validate_yearly_limit(combined_df, temp_df)
            violations.extend(yearly_violations)
            self.monitor.checkpoint("年累计限额校验完成")
            
            # 户累计发放限制校验
            lifetime_violations = self._validate_lifetime_limit(combined_df, temp_df)
            violations.extend(lifetime_violations)
            self.monitor.checkpoint("户累计限额校验完成")
            
            # 更新统计表中的超限次数
            if violations:
                self._update_violation_stats(violations, temp_df)
                self.monitor.checkpoint("违规统计更新完成")
            
            self.monitor.end_monitoring("限额规则校验")
            
            is_passed = len(violations) == 0
            self.logger.info(f"限额规则校验完成: {'通过' if is_passed else '未通过'}, "
                           f"发现 {len(violations)} 个违规情况")
            
            return violations, is_passed
            
        except Exception as e:
            self.logger.error(f"执行限额规则校验时发生错误: {e}")
            raise
    
    def _load_rules_config(self) -> Optional[pd.DataFrame]:
        """
        加载规则配置表
        
        Returns:
            规则配置数据框或None
        """
        try:
            raw_data_path = self.config.get('data_paths.raw_data')
            rules_file = self.config.get('table_files.rules_config_table')
            file_path = Path(raw_data_path) / rules_file
            
            if not file_path.exists():
                self.logger.error(f"规则配置文件不存在: {file_path}")
                return None
            
            rules_df = pd.read_csv(file_path, encoding='utf-8-sig')
            
            # 过滤生效的规则
            active_rules = rules_df[rules_df['是否生效'] == '是'].copy()
            
            self.logger.info(f"加载规则配置成功: 共 {len(rules_df)} 条规则，"
                           f"其中 {len(active_rules)} 条生效")
            
            return active_rules
            
        except Exception as e:
            self.logger.error(f"加载规则配置时发生错误: {e}")
            return None
    
    def _load_customer_stats(self) -> Optional[pd.DataFrame]:
        """
        加载客户统计表
        
        Returns:
            客户统计数据框或None
        """
        try:
            raw_data_path = self.config.get('data_paths.raw_data')
            stats_file = self.config.get('table_files.customer_stats_table')
            file_path = Path(raw_data_path) / stats_file
            
            if not file_path.exists():
                self.logger.warning(f"客户统计文件不存在: {file_path}，将创建空表")
                return pd.DataFrame()
            
            # 指定需要保持为字符串类型的列
            dtype_dict = {
                '客户手机号': str,
                '客户名称': str,
                '客户归属企业': str,
                '申请发放机构': str,
                '活动方案全称': str,
                '归属条线': str,
                '备注（非必填）': str
            }
            
            stats_df = pd.read_csv(file_path, encoding='utf-8-sig', dtype=dtype_dict)
            self.logger.info(f"加载客户统计表成功: 共 {len(stats_df)} 条记录")
            
            return stats_df
            
        except Exception as e:
            self.logger.error(f"加载客户统计表时发生错误: {e}")
            return pd.DataFrame()
    
    def _load_yearly_stats(self) -> Optional[pd.DataFrame]:
        """
        加载年度统计表
        
        Returns:
            年度统计数据框或None
        """
        try:
            raw_data_path = self.config.get('data_paths.raw_data')
            stats_file = self.config.get('table_files.yearly_stats_table')
            file_path = Path(raw_data_path) / stats_file
            
            if not file_path.exists():
                self.logger.warning(f"年度统计文件不存在: {file_path}，将创建空表")
                return pd.DataFrame()
            
            # 指定需要保持为字符串类型的列
            dtype_dict = {
                '客户手机号': str,
                '客户名称': str,
                '客户归属企业': str,
                '申请发放机构': str,
                '活动方案全称': str,
                '归属条线': str,
                '备注（非必填）': str
            }
            
            stats_df = pd.read_csv(file_path, encoding='utf-8-sig', dtype=dtype_dict)
            self.logger.info(f"加载年度统计表成功: 共 {len(stats_df)} 条记录")
            
            return stats_df
            
        except Exception as e:
            self.logger.error(f"加载年度统计表时发生错误: {e}")
            return pd.DataFrame()
    
    def _get_rule_limit(self, rule_code: str) -> Optional[Tuple[str, float]]:
        """
        获取规则限制值
        
        Args:
            rule_code: 规则代码
            
        Returns:
            (规则名称, 限制数值) 或 None
        """
        try:
            if self.rules_config is None or self.rules_config.empty:
                return None
            
            # 处理规则代码映射
            rule_codes = self.config.get('limit_rules.rule_codes', {})
            
            # 查找对应的规则代码
            actual_rule_code = None
            if rule_code == 'single_limit':
                actual_rule_code = rule_codes.get('single_limit', 'SINGLE_LIMIT')
            elif rule_code == 'yearly_limit':
                # 注意配置表中是YEAR_LIMIT，不是YEARLY_LIMIT
                actual_rule_code = 'YEARLY_LIMIT'
            elif rule_code == 'lifetime_limit':
                actual_rule_code = rule_codes.get('lifetime_limit', 'CUSTOMER_TOTAL_LIMIT')
            
            if actual_rule_code is None:
                return None
            
            rule_row = self.rules_config[self.rules_config['规则代码'] == actual_rule_code]
            
            if rule_row.empty:
                self.logger.warning(f"未找到规则代码: {actual_rule_code}")
                return None
            
            rule_name = rule_row.iloc[0]['规则名称']
            limit_value = float(rule_row.iloc[0]['限制数值'])
            
            return rule_name, limit_value
            
        except Exception as e:
            self.logger.error(f"获取规则限制值时发生错误: {e}")
            return None

    def _validate_single_limit(self, combined_df: pd.DataFrame,
                              temp_df: pd.DataFrame) -> List[LimitViolation]:
        """
        校验单次发放限制

        Args:
            combined_df: 合并数据
            temp_df: 临时表数据

        Returns:
            违规记录列表
        """
        try:
            violations = []
            rule_info = self._get_rule_limit('single_limit')

            if rule_info is None:
                self.logger.warning("未找到单次发放限制规则配置")
                return violations

            rule_name, limit_value = rule_info
            self.logger.info(f"执行单次发放限制校验: {rule_name}, 限制值: {limit_value}")

            # 按单次统计主键分组
            key_fields = self.config.get('limit_rules.statistical_keys.single_limit', [])
            existing_fields = [field for field in key_fields if field in combined_df.columns]

            if not existing_fields:
                self.logger.warning("单次发放限制校验所需字段不完整")
                return violations

            # 确保客户归属企业字段存在且正确处理空值
            if '客户归属企业' in combined_df.columns:
                # 将NaN值替换为特殊标记，确保空值被作为独立分组处理
                combined_df_for_grouping = combined_df.copy()
                # pandas 的 groupby 函数默认会忽略空值（NaN），即所有 NaN 的行会被排除在分组之外，而不是作为一个独立分组。
                combined_df_for_grouping['客户归属企业'] = combined_df_for_grouping['客户归属企业'].fillna('__NULL__')
            else:
                combined_df_for_grouping = combined_df

            # 按关键字段分组并计算总金额
            grouped = combined_df_for_grouping.groupby(existing_fields)

            for key_values, group in grouped:
                total_amount = group['等值金豆发放数量'].sum()

                if total_amount > limit_value:
                    # 构建客户关键信息，将特殊标记转换回空值
                    customer_key = {}
                    # zip(...) 会生成 [('客户名称', '张三'), ('客户手机号', '13800138000'), ('客户归属企业', '__NULL__')] 的配对
                    for field, value in zip(existing_fields, key_values):
                        if field == '客户归属企业' and value == '__NULL__':
                            customer_key[field] = ''   # 还原为空字符串
                        else:
                            customer_key[field] = value

                    # 获取违规记录
                    violation_records = group.to_dict('records')

                    violation = LimitViolation(
                        rule_code='SINGLE_LIMIT',
                        rule_name=rule_name,
                        customer_key=customer_key,
                        current_amount=total_amount,
                        limit_amount=limit_value,
                        violation_records=violation_records
                    )
                    violations.append(violation)

                    self.logger.debug(f"发现单次发放超限: {customer_key}, "
                                    f"当前金额: {total_amount}, 限制: {limit_value}")

            return violations

        except Exception as e:
            self.logger.error(f"校验单次发放限制时发生错误: {e}")
            return []

    def _validate_yearly_limit(self, combined_df: pd.DataFrame,
                              temp_df: pd.DataFrame) -> List[LimitViolation]:
        """
        校验年累计发放限制

        Args:
            combined_df: 合并数据
            temp_df: 临时表数据

        Returns:
            违规记录列表
        """
        try:
            violations = []
            rule_info = self._get_rule_limit('yearly_limit')

            if rule_info is None:
                self.logger.warning("未找到年累计发放限制规则配置")
                return violations

            rule_name, limit_value = rule_info
            self.logger.info(f"执行年累计发放限制校验: {rule_name}, 限制值: {limit_value}")

            # 添加年份字段
            combined_df_with_year = combined_df.copy()
            combined_df_with_year['年份'] = pd.to_datetime(
                combined_df_with_year['申请发放日期']
            ).dt.year

            # 确保客户归属企业字段存在且正确处理空值
            if '客户归属企业' in combined_df_with_year.columns:
                # 将NaN值替换为特殊标记，确保空值被作为独立分组处理
                combined_df_with_year['客户归属企业'] = combined_df_with_year['客户归属企业'].fillna('__NULL__')

            # 按年统计主键分组
            key_fields = self.config.get('limit_rules.statistical_keys.yearly_limit', [])
            existing_fields = [field for field in key_fields if field in combined_df_with_year.columns]

            if not existing_fields:
                self.logger.warning("年累计发放限制校验所需字段不完整")
                return violations

            # 按关键字段分组并计算总金额
            grouped = combined_df_with_year.groupby(existing_fields)

            for key_values, group in grouped:
                total_amount = group['等值金豆发放数量'].sum()

                if total_amount > limit_value:
                    # 构建客户关键信息，将特殊标记转换回空值
                    customer_key = {}
                    for field, value in zip(existing_fields, key_values):
                        if field == '客户归属企业' and value == '__NULL__':
                            customer_key[field] = ''
                        else:
                            customer_key[field] = value

                    # 获取违规记录
                    violation_records = group.to_dict('records')

                    violation = LimitViolation(
                        rule_code='YEARLY_LIMIT',
                        rule_name=rule_name,
                        customer_key=customer_key,
                        current_amount=total_amount,
                        limit_amount=limit_value,
                        violation_records=violation_records
                    )
                    violations.append(violation)

                    self.logger.debug(f"发现年累计发放超限: {customer_key}, "
                                    f"当前金额: {total_amount}, 限制: {limit_value}")

            return violations

        except Exception as e:
            self.logger.error(f"校验年累计发放限制时发生错误: {e}")
            return []

    def _validate_lifetime_limit(self, combined_df: pd.DataFrame,
                                temp_df: pd.DataFrame) -> List[LimitViolation]:
        """
        校验户累计发放限制

        Args:
            combined_df: 合并数据
            temp_df: 临时表数据

        Returns:
            违规记录列表
        """
        try:
            violations = []
            rule_info = self._get_rule_limit('lifetime_limit')

            if rule_info is None:
                self.logger.warning("未找到户累计发放限制规则配置")
                return violations

            rule_name, limit_value = rule_info
            self.logger.info(f"执行户累计发放限制校验: {rule_name}, 限制值: {limit_value}")

            # 按户统计主键分组
            key_fields = self.config.get('limit_rules.statistical_keys.lifetime_limit', [])
            existing_fields = [field for field in key_fields if field in combined_df.columns]

            if not existing_fields:
                self.logger.warning("户累计发放限制校验所需字段不完整")
                return violations

            # 确保客户归属企业字段存在且正确处理空值
            if '客户归属企业' in combined_df.columns:
                # 将NaN值替换为特殊标记，确保空值被作为独立分组处理
                combined_df_for_grouping = combined_df.copy()
                combined_df_for_grouping['客户归属企业'] = combined_df_for_grouping['客户归属企业'].fillna('__NULL__')
            else:
                combined_df_for_grouping = combined_df

            # 按关键字段分组并计算总金额
            grouped = combined_df_for_grouping.groupby(existing_fields)

            for key_values, group in grouped:
                total_amount = group['等值金豆发放数量'].sum()

                if total_amount > limit_value:
                    # 构建客户关键信息，将特殊标记转换回空值
                    customer_key = {}
                    for field, value in zip(existing_fields, key_values):
                        if field == '客户归属企业' and value == '__NULL__':
                            customer_key[field] = ''
                        else:
                            customer_key[field] = value

                    # 获取违规记录
                    violation_records = group.to_dict('records')

                    violation = LimitViolation(
                        rule_code='CUSTOMER_TOTAL_LIMIT',
                        rule_name=rule_name,
                        customer_key=customer_key,
                        current_amount=total_amount,
                        limit_amount=limit_value,
                        violation_records=violation_records
                    )
                    violations.append(violation)

                    self.logger.debug(f"发现户累计发放超限: {customer_key}, "
                                    f"当前金额: {total_amount}, 限制: {limit_value}")

            return violations

        except Exception as e:
            self.logger.error(f"校验户累计发放限制时发生错误: {e}")
            return []

    def _update_violation_stats(self, violations: List[LimitViolation], temp_df: pd.DataFrame) -> None:
        """
        更新违规统计信息

        Args:
            violations: 违规记录列表
            temp_df: 临时表数据
        """
        try:
            self.logger.info("开始更新违规统计信息")

            for violation in violations:
                if violation.rule_code == 'SINGLE_LIMIT':
                    self._update_single_violation_stats(violation, temp_df)
                elif violation.rule_code == 'YEARLY_LIMIT':
                    self._update_yearly_violation_stats(violation, temp_df)
                elif violation.rule_code == 'CUSTOMER_TOTAL_LIMIT':
                    self._update_lifetime_violation_stats(violation, temp_df)

            # 保存更新后的统计表
            self._save_updated_stats()

            self.logger.info("违规统计信息更新完成")

        except Exception as e:
            self.logger.error(f"更新违规统计信息时发生错误: {e}")

    def _update_single_violation_stats(self, violation: LimitViolation, temp_df: pd.DataFrame) -> None:
        """
        更新单次违规统计

        Args:
            violation: 违规记录
            temp_df: 临时表数据
        """
        try:
            customer_key = violation.customer_key
            customer_name = customer_key.get('客户名称')
            customer_phone = customer_key.get('客户手机号')
            customer_enterprise = customer_key.get('客户归属企业', '')

            # 正确处理客户归属企业为空值的情况
            if customer_enterprise == '':
                # 客户归属企业为空时，查找客户归属企业为空的记录
                customer_mask = (
                    (self.customer_stats['客户名称'] == customer_name) &
                    (self.customer_stats['客户手机号'] == customer_phone) &
                    (self.customer_stats['客户归属企业'].isna() | (self.customer_stats['客户归属企业'] == ''))
                )
            else:
                # 客户归属企业不为空时，精确匹配
                customer_mask = (
                    (self.customer_stats['客户名称'] == customer_name) &
                    (self.customer_stats['客户手机号'] == customer_phone) &
                    (self.customer_stats['客户归属企业'] == customer_enterprise)
                )

            if customer_mask.any():
                # 更新现有记录
                idx = self.customer_stats[customer_mask].index[0]
                self.customer_stats.loc[idx, '单次发放超规定次数'] = \
                    self.customer_stats.loc[idx, '单次发放超规定次数'] + 1

                # 更新申请发放机构
                self._update_customer_institutions(idx, temp_df, customer_name,
                                                 customer_phone, customer_enterprise)
            else:
                # 创建新记录
                self._create_customer_stats_record(customer_name, customer_phone,
                                                 customer_enterprise, temp_df,
                                                 single_violation=True)

        except Exception as e:
            self.logger.error(f"更新单次违规统计时发生错误: {e}")

    def _update_yearly_violation_stats(self, violation: LimitViolation, temp_df: pd.DataFrame) -> None:
        """
        更新年度违规统计

        Args:
            violation: 违规记录
            temp_df: 临时表数据
        """
        try:
            customer_key = violation.customer_key
            customer_name = customer_key.get('客户名称')
            customer_phone = customer_key.get('客户手机号')
            customer_enterprise = customer_key.get('客户归属企业', '')
            year = customer_key.get('年份')

            # 正确处理客户归属企业为空值的情况
            if customer_enterprise == '':
                # 客户归属企业为空时，查找客户归属企业为空的记录
                yearly_mask = (
                    (self.yearly_stats['客户名称'] == customer_name) &
                    (self.yearly_stats['客户手机号'] == customer_phone) &
                    (self.yearly_stats['客户归属企业'].isna() | (self.yearly_stats['客户归属企业'] == '')) &
                    (self.yearly_stats['发放年份'] == year)
                )
            else:
                # 客户归属企业不为空时，精确匹配
                yearly_mask = (
                    (self.yearly_stats['客户名称'] == customer_name) &
                    (self.yearly_stats['客户手机号'] == customer_phone) &
                    (self.yearly_stats['客户归属企业'] == customer_enterprise) &
                    (self.yearly_stats['发放年份'] == year)
                )

            if yearly_mask.any():
                # 更新现有记录
                idx = self.yearly_stats[yearly_mask].index[0]
                self.yearly_stats.loc[idx, '年累计超规定次数'] = \
                    self.yearly_stats.loc[idx, '年累计超规定次数'] + 1

                # 更新申请发放机构
                self._update_yearly_institutions(idx, temp_df, customer_name,
                                               customer_phone, customer_enterprise)
            else:
                # 创建新记录
                self._create_yearly_stats_record(customer_name, customer_phone,
                                               customer_enterprise, year, temp_df,
                                               yearly_violation=True)

        except Exception as e:
            self.logger.error(f"更新年度违规统计时发生错误: {e}")

    def _update_lifetime_violation_stats(self, violation: LimitViolation, temp_df: pd.DataFrame) -> None:
        """
        更新户累计违规统计

        Args:
            violation: 违规记录
            temp_df: 临时表数据
        """
        try:
            customer_key = violation.customer_key
            customer_name = customer_key.get('客户名称')
            customer_phone = customer_key.get('客户手机号')
            customer_enterprise = customer_key.get('客户归属企业', '')

            # 正确处理客户归属企业为空值的情况
            if customer_enterprise == '':
                # 客户归属企业为空时，查找客户归属企业为空的记录
                customer_mask = (
                    (self.customer_stats['客户名称'] == customer_name) &
                    (self.customer_stats['客户手机号'] == customer_phone) &
                    (self.customer_stats['客户归属企业'].isna() | (self.customer_stats['客户归属企业'] == ''))
                )
            else:
                # 客户归属企业不为空时，精确匹配
                customer_mask = (
                    (self.customer_stats['客户名称'] == customer_name) &
                    (self.customer_stats['客户手机号'] == customer_phone) &
                    (self.customer_stats['客户归属企业'] == customer_enterprise)
                )

            if customer_mask.any():
                # 更新现有记录
                idx = self.customer_stats[customer_mask].index[0]
                self.customer_stats.loc[idx, '户累计超规定次数'] = \
                    self.customer_stats.loc[idx, '户累计超规定次数'] + 1

                # 更新申请发放机构
                self._update_customer_institutions(idx, temp_df, customer_name,
                                                 customer_phone, customer_enterprise)
            else:
                # 创建新记录
                self._create_customer_stats_record(customer_name, customer_phone,
                                                 customer_enterprise, temp_df,
                                                 lifetime_violation=True)

        except Exception as e:
            self.logger.error(f"更新户累计违规统计时发生错误: {e}")

    def _create_customer_stats_record(self, customer_name: str, customer_phone: str,
                                    customer_enterprise: str, temp_df: pd.DataFrame,
                                    single_violation: bool = False,
                                    lifetime_violation: bool = False) -> None:
        """
        创建客户统计记录

        Args:
            customer_name: 客户名称
            customer_phone: 客户手机号
            customer_enterprise: 客户归属企业
            temp_df: 临时表数据
            single_violation: 是否为单次违规
            lifetime_violation: 是否为户累计违规
        """
        try:
            # 正确处理客户归属企业为空值的情况
            if customer_enterprise == '':
                # 客户归属企业为空时，查找客户归属企业为空的记录
                customer_temp_data = temp_df[
                    (temp_df['客户名称'] == customer_name) &
                    (temp_df['客户手机号'] == customer_phone) &
                    (temp_df['客户归属企业'].isna() | (temp_df['客户归属企业'] == ''))
                ]
            else:
                # 客户归属企业不为空时，精确匹配
                customer_temp_data = temp_df[
                    (temp_df['客户名称'] == customer_name) &
                    (temp_df['客户手机号'] == customer_phone) &
                    (temp_df['客户归属企业'] == customer_enterprise)
                ]

            institutions = customer_temp_data['申请发放机构'].unique()
            institutions_str = ','.join([str(inst) for inst in institutions if pd.notna(inst)])

            # 创建新记录
            new_record = {
                '记录ID': len(self.customer_stats) + 1,
                '客户名称': customer_name,
                '客户手机号': customer_phone,
                '客户归属企业': customer_enterprise,
                '等值金豆发放户累计': 0,
                '等值金豆发放总次数': 0,
                '申请发放机构': institutions_str,
                '单次发放超规定次数': 1 if single_violation else 0,
                '户累计超规定次数': 1 if lifetime_violation else 0,
                '备注': ''
            }

            # 添加到统计表
            self.customer_stats = pd.concat([self.customer_stats, pd.DataFrame([new_record])],
                                          ignore_index=True)

        except Exception as e:
            self.logger.error(f"创建客户统计记录时发生错误: {e}")

    def _create_yearly_stats_record(self, customer_name: str, customer_phone: str,
                                  customer_enterprise: str, year: int, temp_df: pd.DataFrame,
                                  yearly_violation: bool = False) -> None:
        """
        创建年度统计记录

        Args:
            customer_name: 客户名称
            customer_phone: 客户手机号
            customer_enterprise: 客户归属企业
            year: 年份
            temp_df: 临时表数据
            yearly_violation: 是否为年度违规
        """
        try:
            # 正确处理客户归属企业为空值的情况
            if customer_enterprise == '':
                # 客户归属企业为空时，查找客户归属企业为空的记录
                customer_temp_data = temp_df[
                    (temp_df['客户名称'] == customer_name) &
                    (temp_df['客户手机号'] == customer_phone) &
                    (temp_df['客户归属企业'].isna() | (temp_df['客户归属企业'] == ''))
                ]
            else:
                # 客户归属企业不为空时，精确匹配
                customer_temp_data = temp_df[
                    (temp_df['客户名称'] == customer_name) &
                    (temp_df['客户手机号'] == customer_phone) &
                    (temp_df['客户归属企业'] == customer_enterprise)
                ]

            institutions = customer_temp_data['申请发放机构'].unique()
            institutions_str = ','.join([str(inst) for inst in institutions if pd.notna(inst)])

            # 创建新记录
            new_record = {
                '记录ID': len(self.yearly_stats) + 1,
                '客户名称': customer_name,
                '客户手机号': customer_phone,
                '客户归属企业': customer_enterprise,
                '申请发放机构': institutions_str,
                '发放年份': year,
                '等值金豆发放总次数': 0,
                '等值金豆发放年累计': 0,
                '年累计超规定次数': 1 if yearly_violation else 0,
                '备注': ''
            }

            # 添加到统计表
            self.yearly_stats = pd.concat([self.yearly_stats, pd.DataFrame([new_record])],
                                        ignore_index=True)

        except Exception as e:
            self.logger.error(f"创建年度统计记录时发生错误: {e}")

    def _update_customer_institutions(self, idx: int, temp_df: pd.DataFrame,
                                    customer_name: str, customer_phone: str,
                                    customer_enterprise: str) -> None:
        """
        更新客户统计表中的申请发放机构字段

        Args:
            idx: 记录索引
            temp_df: 临时表数据
            customer_name: 客户名称
            customer_phone: 客户手机号
            customer_enterprise: 客户归属企业
        """
        try:
            # 正确处理客户归属企业为空值的情况
            if customer_enterprise == '':
                # 客户归属企业为空时，查找客户归属企业为空的记录
                customer_temp_data = temp_df[
                    (temp_df['客户名称'] == customer_name) &
                    (temp_df['客户手机号'] == customer_phone) &
                    (temp_df['客户归属企业'].isna() | (temp_df['客户归属企业'] == ''))
                ]
            else:
                # 客户归属企业不为空时，精确匹配
                customer_temp_data = temp_df[
                    (temp_df['客户名称'] == customer_name) &
                    (temp_df['客户手机号'] == customer_phone) &
                    (temp_df['客户归属企业'] == customer_enterprise)
                ]

            new_institutions = set(customer_temp_data['申请发放机构'].dropna().unique())

            # 获取现有的申请发放机构
            existing_institutions_str = self.customer_stats.loc[idx, '申请发放机构']
            if pd.isna(existing_institutions_str) or existing_institutions_str == '':
                existing_institutions = set()
            else:
                existing_institutions = set(existing_institutions_str.split(','))

            # 合并机构列表
            all_institutions = existing_institutions.union(new_institutions)
            updated_institutions_str = ','.join(sorted(all_institutions))

            # 更新记录
            self.customer_stats.loc[idx, '申请发放机构'] = updated_institutions_str

        except Exception as e:
            self.logger.error(f"更新客户申请发放机构时发生错误: {e}")

    def _update_yearly_institutions(self, idx: int, temp_df: pd.DataFrame,
                                  customer_name: str, customer_phone: str,
                                  customer_enterprise: str) -> None:
        """
        更新年度统计表中的申请发放机构字段

        Args:
            idx: 记录索引
            temp_df: 临时表数据
            customer_name: 客户名称
            customer_phone: 客户手机号
            customer_enterprise: 客户归属企业
        """
        try:
            # 正确处理客户归属企业为空值的情况
            if customer_enterprise == '':
                # 客户归属企业为空时，查找客户归属企业为空的记录
                customer_temp_data = temp_df[
                    (temp_df['客户名称'] == customer_name) &
                    (temp_df['客户手机号'] == customer_phone) &
                    (temp_df['客户归属企业'].isna() | (temp_df['客户归属企业'] == ''))
                ]
            else:
                # 客户归属企业不为空时，精确匹配
                customer_temp_data = temp_df[
                    (temp_df['客户名称'] == customer_name) &
                    (temp_df['客户手机号'] == customer_phone) &
                    (temp_df['客户归属企业'] == customer_enterprise)
                ]

            new_institutions = set(customer_temp_data['申请发放机构'].dropna().unique())

            # 获取现有的申请发放机构
            existing_institutions_str = self.yearly_stats.loc[idx, '申请发放机构']
            if pd.isna(existing_institutions_str) or existing_institutions_str == '':
                existing_institutions = set()
            else:
                existing_institutions = set(existing_institutions_str.split(','))

            # 合并机构列表
            all_institutions = existing_institutions.union(new_institutions)
            updated_institutions_str = ','.join(sorted(all_institutions))

            # 更新记录
            self.yearly_stats.loc[idx, '申请发放机构'] = updated_institutions_str

        except Exception as e:
            self.logger.error(f"更新年度申请发放机构时发生错误: {e}")

    def _save_updated_stats(self) -> None:
        """保存更新后的统计表"""
        try:
            raw_data_path = self.config.get('data_paths.raw_data')

            # 保存客户统计表
            if self.customer_stats is not None:
                customer_file = Path(raw_data_path) / self.config.get('table_files.customer_stats_table')
                self.customer_stats.to_csv(customer_file, index=False, encoding='utf-8-sig')
                self.logger.info(f"客户统计表保存成功: {customer_file}")

            # 保存年度统计表
            if self.yearly_stats is not None:
                yearly_file = Path(raw_data_path) / self.config.get('table_files.yearly_stats_table')
                self.yearly_stats.to_csv(yearly_file, index=False, encoding='utf-8-sig')
                self.logger.info(f"年度统计表保存成功: {yearly_file}")

        except Exception as e:
            self.logger.error(f"保存统计表时发生错误: {e}")

    def generate_violation_report(self, violations: List[LimitViolation]) -> pd.DataFrame:
        """
        生成违规报告

        Args:
            violations: 违规记录列表

        Returns:
            违规报告数据框
        """
        try:
            if not violations:
                self.logger.info("没有发现限额违规")
                return pd.DataFrame()

            # 转换为数据框
            report_data = []
            for i, violation in enumerate(violations, 1):
                base_info = violation.to_dict()
                base_info['违规组编号'] = i

                # 为每个违规记录创建一行
                for j, record in enumerate(violation.violation_records):
                    row = base_info.copy()
                    row['记录序号'] = j + 1
                    
                    # 确保所有值都转换为字符串，避免类型错误
                    processed_record = {}
                    for key, value in record.items():
                        if pd.isna(value):
                            processed_record[key] = ''
                        else:
                            processed_record[key] = str(value)
                    
                    # 确保数据来源信息正确显示
                    if '数据来源' not in processed_record:
                        processed_record['数据来源'] = '未知'
                    if '源文件名' not in processed_record:
                        processed_record['源文件名'] = '未知文件'
                    
                    row.update(processed_record)
                    report_data.append(row)

            report_df = pd.DataFrame(report_data)

            self.logger.info(f"违规报告生成完成，共 {len(violations)} 组违规，"
                           f"涉及 {len(report_data)} 条记录")

            return report_df

        except Exception as e:
            self.logger.error(f"生成违规报告时发生错误: {e}")
            return pd.DataFrame()

    def get_violation_summary(self, violations: List[LimitViolation]) -> Dict[str, Any]:
        """
        获取违规摘要

        Args:
            violations: 违规记录列表

        Returns:
            违规摘要字典
        """
        try:
            # 按规则类型统计
            rule_stats = defaultdict(lambda: {'count': 0, 'total_excess': 0, 'customers': set()})

            for violation in violations:
                rule_code = violation.rule_code
                rule_stats[rule_code]['count'] += 1
                rule_stats[rule_code]['total_excess'] += violation.excess_amount

                # 添加客户信息
                customer_key = str(violation.customer_key)
                rule_stats[rule_code]['customers'].add(customer_key)

            # 转换为可序列化的格式
            summary = {
                '总违规组数': len(violations),
                '总违规记录数': sum(len(v.violation_records) for v in violations),
                '按规则统计': {}
            }

            for rule_code, stats in rule_stats.items():
                summary['按规则统计'][rule_code] = {
                    '违规组数': stats['count'],
                    '总超限金额': stats['total_excess'],
                    '涉及客户数': len(stats['customers'])
                }

            return summary

        except Exception as e:
            self.logger.error(f"生成违规摘要时发生错误: {e}")
            return {}


# TODO: 扩展点 - 限额规则校验功能扩展
# 1. 动态规则配置和热更新
# 2. 规则优先级和依赖关系管理
# 3. 自定义规则表达式支持
# 4. 规则执行性能监控和优化
# 5. 规则违规预警和通知机制
