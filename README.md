# 金豆发放申请校验系统

## 项目概述和目标

本系统是一个金豆发放申请的批处理校验系统，主要用于处理金豆发放申请数据的清洗、校验、冲突检测和限额规则校验等功能。系统支持批量导入多个申请明细表，通过多层校验机制确保数据质量和业务规则合规性。

### 核心目标
- 实现高效的数据清洗和格式标准化
- 提供完整的重复数据和冲突检测机制
- 执行严格的限额规则校验（单次、年累计、户累计）
- 维护准确的统计数据和历史记录
- 确保数据处理的原子性和一致性

## 功能列表及详细说明

### 1. 数据清洗与转换模块
- **日期格式标准化**：统一申请发放日期和申请填报日期为YYYY-MM-DD格式
- **字符串清理**：去除客户名称、客户手机号等关键字段的前后空格
- **数据格式校验**：手机号格式、金豆发放数量有效性、必填字段检查
- **业务规则说明过滤**：动态识别并过滤业务规则说明信息
- **空行识别**：正确识别空行，避免误报数据质量问题

### 2. 临时汇总表生成模块
- 将所有清洗后的明细表记录汇总到临时表
- 为后续校验提供统一的数据源

### 3. 数据重复与冲突校验模块
- **重复校验**：基于6字段组合检测完全重复记录
- **客户信息冲突校验**：检测相同客户的信息不一致问题
- **性能优化**：采用高效算法降低大数据量场景下的计算复杂度

### 4. 限额规则校验引擎
- **单次发放限制**：同一客户相同日期的发放总额校验
- **年累计发放限制**：客户年度累计发放额度校验
- **户累计发放限制**：客户历史累计发放额度校验
- **规则配置驱动**：基于配置表动态加载校验规则

### 5. 数据处理与更新模块
- **数据备份**：自动备份关键数据表
- **主明细表追加**：将通过校验的记录追加到主数据表
- **统计表更新**：维护户统计表和年统计表的准确性
- **事务管理**：确保数据操作的原子性

## 使用方法

### 快速开始
```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 配置数据路径（编辑 config.yaml）
# 确保以下路径设置正确：
# - data_paths.raw_data: 原始数据目录
# - data_paths.temp_data: 临时数据目录
# - data_paths.output_data: 输出数据目录

# 3. 运行系统测试
python test_system.py

# 4. 运行完整校验流程
python run_validation.py
```

### 详细使用说明

#### 1. 数据准备
将申请明细表文件放入 `data/raw_data` 目录，文件名格式：
- `*申请明细表*.csv` 或 `*申请明细表*.xlsx`
- 例如：`2024年1月金豆申请明细表.csv`

#### 2. 运行校验
```bash
# 使用默认配置运行
python run_validation.py

# 使用自定义配置文件
python run_validation.py --config custom_config.yaml

# 查看帮助信息
python run_validation.py --help
```

#### 3. 校验流程
系统将按以下顺序执行：

1. **数据清洗与转换**
   - 读取所有申请明细表文件
   - 标准化日期格式和数据类型
   - 过滤无效和说明性数据
   - 生成临时汇总表

2. **重复和冲突检测**
   - 检测完全重复的申请记录
   - 检测客户信息冲突（同手机号不同姓名等）
   - 用户可选择继续处理或停止查看详情

3. **限额规则校验**
   - 单次发放限制校验
   - 年累计发放限制校验
   - 户累计发放限制校验
   - 发现违规将停止处理

4. **数据处理与更新**
   - 备份现有数据表
   - 追加有效数据到主明细表
   - 更新客户统计表和年度统计表

#### 4. 结果查看
校验完成后，可在以下位置查看结果：
- `data/output_data/`: 各类报告文件
- `data/backup_data/`: 数据备份文件
- `logs/`: 详细日志文件

### 参数说明
- `--config`: 指定配置文件路径（默认：config.yaml）
- `--version`: 显示版本信息
- `--help`: 显示帮助信息

### 返回值说明
- 返回码 0：校验成功完成，数据已更新
- 返回码 1：校验失败或发现问题需要处理
- 返回码 130：用户中断操作

### 交互式操作
当检测到重复或冲突数据时，系统会提示用户选择：
1. 继续处理（忽略重复和冲突）
2. 停止处理，查看详细报告
3. 退出系统

### 错误处理
- 数据质量问题：系统会生成详细报告并停止处理
- 限额违规：系统会生成违规报告并停止处理
- 系统错误：检查日志文件获取详细错误信息

### 编程接口使用
```python
from src.main import GoldBeanValidationSystem

# 初始化系统
system = GoldBeanValidationSystem("config.yaml")

# 执行完整校验流程
result = system.run_validation_process()

# 查看处理结果
print(result.summary)
if result.success:
    print("校验成功完成")
else:
    print(f"校验失败，当前阶段：{result.stage}")
```

## 安装和配置指南

### 环境要求
- Python 3.8+
- pandas >= 1.3.0
- numpy >= 1.21.0
- PyYAML >= 6.0
- openpyxl >= 3.0.0（用于Excel文件处理）

### 依赖安装
```bash
pip install -r requirements.txt
```

### 目录结构
```
金豆发放申请校验/
├── src/                    # 源代码目录
│   ├── main.py            # 主程序
│   ├── utils.py           # 工具类
│   ├── data_cleaning.py   # 数据清洗模块
│   ├── duplicate_conflict_detector.py  # 重复冲突检测
│   ├── limit_rule_engine.py            # 限额规则引擎
│   └── data_processor.py  # 数据处理模块
├── data/                   # 数据目录
│   ├── raw_data/          # 原始数据文件
│   ├── temp_data/         # 临时数据文件
│   ├── backup_data/       # 备份文件
│   └── output_data/       # 输出报告文件
├── logs/                   # 日志文件目录
├── config.yaml            # 配置文件
├── requirements.txt       # 依赖包列表
├── run_validation.py      # 运行脚本
├── test_system.py         # 测试脚本
└── README.md              # 说明文档
```

### 配置步骤

#### 1. 创建目录结构
```bash
mkdir -p data/{raw_data,temp_data,backup_data,output_data}
mkdir -p logs
```

#### 2. 配置文件设置
编辑 `config.yaml` 文件，确保路径配置正确：
```yaml
data_paths:
  raw_data: "data/raw_data"
  temp_data: "data/temp_data"
  backup_data: "data/backup_data"
  output_data: "data/output_data"

table_files:
  customer_stats_table: "金豆发放户统计表.csv"
  main_detail_table: "金豆发放申请主明细表.csv"
  yearly_stats_table: "金豆发放年统计表.csv"
  rule_config_table: "金豆发放规则配置表.csv"
```

#### 3. 准备数据文件
将以下文件放入 `data/raw_data/` 目录：
- **申请明细表**：文件名包含"申请明细表"关键字
- **规则配置表**：`金豆发放规则配置表.csv`
- **统计表**（可选）：如果已有历史数据

#### 4. 规则配置表格式
创建 `金豆发放规则配置表.csv`，包含以下列：
```csv
规则代码,规则名称,规则类型,限制金额,是否生效,备注
SINGLE_LIMIT,单次发放限制,single_limit,5000,是,单次发放不超过5000金豆
YEAR_LIMIT,年累计发放限制,yearly_limit,50000,是,年累计不超过50000金豆
CUSTOMER_TOTAL_LIMIT,户累计发放限制,lifetime_limit,100000,是,户累计不超过100000金豆
```

### 数据表结构说明

#### 申请明细表字段
- 记录ID, 客户名称, 客户手机号, 客户归属企业
- 申请发放机构, 申请发放日期, 等值金豆发放数量
- 备注

#### 统计表字段
**户统计表**：
- 记录ID, 客户名称, 客户手机号, 客户归属企业
- 等值金豆发放户累计, 等值金豆发放总次数, 申请发放机构
- 单次发放超规定次数, 户累计超规定次数, 备注

**年统计表**：
- 记录ID, 客户名称, 客户手机号, 客户归属企业
- 申请发放机构, 发放年份, 等值金豆发放总次数
- 等值金豆发放年累计, 年累计超规定次数, 备注

### 性能优化配置
在 `config.yaml` 中可配置：
```yaml
performance:
  batch_size: 10000        # 批处理大小
  max_memory_usage: 0.8    # 最大内存使用率
  enable_parallel: true    # 启用并行处理
  chunk_size: 5000         # 数据块大小
```

### 日志配置
```yaml
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_handler:
    enabled: true
    filename: "logs/validation_system.log"
    max_bytes: 10485760    # 10MB
    backup_count: 5
  console_handler:
    enabled: true
```

### 错误处理和故障排除
- **配置错误**：检查 `config.yaml` 文件格式和路径设置
- **数据格式错误**：查看 `data/output_data/` 中的数据质量报告
- **权限问题**：确保对数据目录有读写权限
- **内存不足**：调整 `performance.batch_size` 和 `performance.chunk_size`
- **详细错误信息**：查看 `logs/` 目录中的日志文件
