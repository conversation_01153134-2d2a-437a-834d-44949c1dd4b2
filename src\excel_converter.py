"""
金豆发放申请校验系统 - Excel到CSV转换模块
将Excel格式的申请明细表转换为CSV格式，供后续处理使用

本模块是数据处理流程的第一个环节，主要功能包括：

核心功能：
1. Excel文件发现：自动扫描指定目录中的Excel文件
2. 格式转换：将.xlsx和.xls文件转换为CSV格式
3. 编码处理：统一使用UTF-8-BOM编码，确保中文正确显示
4. 批量处理：支持多个Excel文件的批量转换
5. 错误处理：完善的异常处理和错误恢复机制

转换流程：
文件扫描 -> 格式验证 -> 内容读取 -> CSV保存 -> 结果统计

支持格式：
- Excel 2007+ (.xlsx)：使用openpyxl引擎
- Excel 97-2003 (.xls)：使用xlrd引擎
- 自动引擎切换：优先使用openpyxl，失败时尝试xlrd

文件处理：
- 输入目录：Data/Raw_xlsx/
- 输出目录：Data/Raw_csv/
- 文件命名：保持原文件名，仅更改扩展名
- 编码格式：UTF-8 with BOM

设计特点：
- 多引擎支持：兼容不同版本的Excel文件
- 编码安全：统一编码格式避免乱码问题
- 批量高效：优化的批量处理算法
- 错误容忍：单个文件失败不影响整体处理
- 详细统计：完整的转换结果统计

质量保证：
- 文件验证：转换前验证Excel文件完整性
- 内容检查：确保转换后CSV文件不为空
- 格式校验：验证文件名包含关键字
- 大小检查：确保转换后文件大小合理

"""

# 导入标准库
import pandas as pd  # 数据处理核心库
import os           # 操作系统接口
import logging      # 日志记录
from pathlib import Path  # 路径操作
from typing import List, Dict, Tuple, Optional, Any  # 类型注解
from datetime import datetime  # 日期时间处理
import shutil       # 高级文件操作
import sys          # 系统相关功能

# 添加src目录到Python路径，确保能够导入自定义模块
sys.path.append(str(Path(__file__).parent))

# 导入自定义工具类
from utils import ConfigManager, LoggerSetup, PerformanceMonitor


class ExcelToCSVConverter:
    """Excel到CSV转换器"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """
        初始化转换器
        
        Args:
            config_path: 配置文件路径
        """
        self.config = ConfigManager(config_path)
        self.logger = LoggerSetup.setup_logger('ExcelToCSVConverter', 
                                             self.config.get('logging', {}))
        self.monitor = PerformanceMonitor(self.config)
        
        # 获取路径配置
        self.raw_excel_path = Path(self.config.get('data_paths.raw_excel', 'Data/Raw_xlsx/'))
        self.raw_csv_path = Path(self.config.get('data_paths.raw_csv', 'Data/Raw_csv/'))
        
        # 确保目录存在
        self.raw_excel_path.mkdir(parents=True, exist_ok=True)
        self.raw_csv_path.mkdir(parents=True, exist_ok=True)
        
        # 转换统计
        self.conversion_stats = {
            'total_files': 0,
            'successful_conversions': 0,
            'failed_conversions': 0,
            'skipped_files': 0,
            'errors': []
        }
    
    def convert_excel_files(self) -> Dict[str, Any]:
        """
        批量转换Excel文件
        
        Returns:
            转换结果统计
        """
        try:
            self.monitor.start_monitoring("Excel批量转换")
            self.logger.info("开始批量转换Excel文件")
            
            # 重置统计信息
            self.conversion_stats = {
                'total_files': 0,
                'successful_conversions': 0,
                'failed_conversions': 0,
                'skipped_files': 0,
                'errors': []
            }
            
            # 查找所有Excel文件
            excel_files = self._find_excel_files()
            
            if not excel_files:
                self.logger.warning("未找到任何Excel文件")
                return self.conversion_stats
            
            self.conversion_stats['total_files'] = len(excel_files)
            self.logger.info(f"发现 {len(excel_files)} 个Excel文件")
            
            # 逐个转换文件
            for i, excel_file in enumerate(excel_files, 1):
                self.logger.info(f"处理第 {i}/{len(excel_files)} 个文件: {excel_file.name}")
                
                try:
                    # 验证文件
                    if not self.validate_excel_file(excel_file):
                        self.conversion_stats['skipped_files'] += 1
                        self.logger.warning(f"跳过无效文件: {excel_file.name}")
                        continue
                    
                    # 转换文件
                    success = self.convert_single_file(excel_file)
                    
                    if success:
                        self.conversion_stats['successful_conversions'] += 1
                        self.logger.info(f"成功转换: {excel_file.name}")
                    else:
                        self.conversion_stats['failed_conversions'] += 1
                        self.logger.error(f"转换失败: {excel_file.name}")
                
                except Exception as e:
                    self.conversion_stats['failed_conversions'] += 1
                    error_msg = f"转换文件 {excel_file.name} 时发生错误: {str(e)}"
                    self.conversion_stats['errors'].append(error_msg)
                    self.logger.error(error_msg)
            
            # 生成转换摘要
            self._generate_conversion_summary()
            
            self.monitor.end_monitoring("Excel批量转换")
            
            return self.conversion_stats
            
        except Exception as e:
            self.logger.error(f"批量转换过程中发生错误: {e}")
            self.conversion_stats['errors'].append(f"批量转换错误: {str(e)}")
            return self.conversion_stats
    
    def convert_single_file(self, excel_file: Path) -> bool:
        """
        转换单个Excel文件
        
        Args:
            excel_file: Excel文件路径
            
        Returns:
            转换是否成功
        """
        try:
            # 生成CSV文件路径，stem()方法返回文件名，不包含扩展名
            csv_filename = excel_file.stem + '.csv'
            csv_file_path = self.raw_csv_path / csv_filename
            
            # 读取Excel文件
            self.logger.info(f"读取Excel文件: {excel_file}")
            
            # 尝试读取Excel文件，处理可能的编码问题
            try:
                df = pd.read_excel(excel_file, engine='openpyxl')
            except Exception as e:
                self.logger.warning(f"使用openpyxl引擎失败，尝试xlrd引擎: {e}")
                try:
                    df = pd.read_excel(excel_file, engine='xlrd')
                except Exception as e2:
                    self.logger.error(f"所有Excel引擎都失败: {e2}")
                    raise e2
            
            # 验证数据内容
            if df.empty:
                self.logger.warning(f"Excel文件为空: {excel_file.name}")
                return False
            
            # 保存为CSV文件，使用UTF-8-BOM编码
            self.logger.info(f"保存CSV文件: {csv_file_path}")
            df.to_csv(csv_file_path, index=False, encoding='utf-8-sig', 
                     errors='replace', quoting=1)  # quoting=1 表示所有字段都加引号
            
            # 验证转换结果
            if not csv_file_path.exists():
                raise Exception("CSV文件创建失败")
            
            # 验证文件大小
            if csv_file_path.stat().st_size == 0:
                raise Exception("生成的CSV文件为空")
            
            self.logger.info(f"成功转换: {excel_file.name} -> {csv_filename}")
            return True
            
        except Exception as e:
            self.logger.error(f"转换文件 {excel_file.name} 失败: {e}")
            return False
    
    def validate_excel_file(self, excel_file: Path) -> bool:
        """
        验证Excel文件格式和内容
        
        Args:
            excel_file: Excel文件路径
            
        Returns:
            文件是否有效
        """
        try:
            # 检查文件是否存在
            if not excel_file.exists():
                self.logger.error(f"文件不存在: {excel_file}")
                return False
            
            # 检查文件扩展名
            if excel_file.suffix.lower() not in ['.xlsx', '.xls']:
                self.logger.warning(f"不支持的文件格式: {excel_file.suffix}")
                return False
            
            # 检查文件名是否包含关键字
            pattern = self.config.get('table_files.detail_table_pattern', '金豆发放申请明细表')
            if pattern not in excel_file.name:
                self.logger.warning(f"文件名不包含关键字 '{pattern}': {excel_file.name}")
                return False
            
            # 检查文件大小
            file_size = excel_file.stat().st_size
            if file_size == 0:
                self.logger.error(f"文件为空: {excel_file.name}")
                return False
            
            # 尝试读取文件头，验证文件完整性
            try:
                # 只读取前几行来验证文件格式
                df_sample = pd.read_excel(excel_file, engine='openpyxl', nrows=5)
                if df_sample.empty:
                    self.logger.warning(f"Excel文件内容为空: {excel_file.name}")
                    return False
            except Exception as e:
                self.logger.error(f"Excel文件格式错误: {excel_file.name}, 错误: {e}")
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"验证文件 {excel_file.name} 时发生错误: {e}")
            return False
    
    def _find_excel_files(self) -> List[Path]:
        """
        查找所有符合条件的Excel文件
        
        Returns:
            Excel文件路径列表
        """
        excel_files = []
        pattern = self.config.get('table_files.detail_table_pattern', '金豆发放申请明细表')
        
        # 查找.xlsx文件
        for file_path in self.raw_excel_path.glob("*.xlsx"):
            if pattern in file_path.name:
                excel_files.append(file_path)
        
        # 查找.xls文件
        for file_path in self.raw_excel_path.glob("*.xls"):
            if pattern in file_path.name:
                excel_files.append(file_path)
        
        # 按文件名排序
        excel_files.sort(key=lambda x: x.name)
        
        return excel_files
    
    def _generate_conversion_summary(self) -> None:
        """生成转换摘要"""
        total = self.conversion_stats['total_files']
        success = self.conversion_stats['successful_conversions']
        failed = self.conversion_stats['failed_conversions']
        skipped = self.conversion_stats['skipped_files']
        
        summary = f"""
Excel转换摘要:
==============
总文件数: {total}
成功转换: {success}
转换失败: {failed}
跳过文件: {skipped}
成功率: {success/total*100:.1f}% (成功/总数)
        """
        
        self.logger.info(summary)
        
        if self.conversion_stats['errors']:
            self.logger.error("转换过程中的错误:")
            for error in self.conversion_stats['errors']:
                self.logger.error(f"  - {error}")
    
    def get_conversion_stats(self) -> Dict[str, Any]:
        """
        获取转换统计信息
        
        Returns:
            转换统计字典
        """
        return self.conversion_stats.copy()
    
    def cleanup_failed_files(self) -> None:
        """清理转换失败的文件"""
        try:
            # 查找可能存在的部分转换文件
            for csv_file in self.raw_csv_path.glob("*.csv"):
                # 检查对应的Excel文件是否存在
                excel_file = self.raw_excel_path / f"{csv_file.stem}.xlsx"
                if not excel_file.exists():
                    excel_file = self.raw_excel_path / f"{csv_file.stem}.xls"
                
                if not excel_file.exists():
                    self.logger.warning(f"删除孤立的CSV文件: {csv_file.name}")
                    csv_file.unlink()
        
        except Exception as e:
            self.logger.error(f"清理失败文件时发生错误: {e}")


def main():
    """主函数，用于独立运行转换模块"""
    try:
        converter = ExcelToCSVConverter()
        result = converter.convert_excel_files()
        
        print("Excel转换完成!")
        print(f"总文件数: {result['total_files']}")
        print(f"成功转换: {result['successful_conversions']}")
        print(f"转换失败: {result['failed_conversions']}")
        print(f"跳过文件: {result['skipped_files']}")
        
        if result['errors']:
            print("\n错误信息:")
            for error in result['errors']:
                print(f"  - {error}")
        
    except Exception as e:
        print(f"转换过程中发生错误: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main()) 