@echo off
chcp 65001 >nul
echo ================================================================
echo                金豆发放申请校验系统
echo ================================================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.8或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

REM 检查配置文件是否存在
if not exist "config.yaml" (
    echo 错误: 配置文件 config.yaml 不存在
    echo 请确保配置文件存在并正确配置
    pause
    exit /b 1
)

REM 检查依赖是否安装
echo 检查依赖包...
python -c "import pandas, numpy, yaml, openpyxl" >nul 2>&1
if errorlevel 1 (
    echo 正在安装依赖包...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo 错误: 依赖包安装失败
        pause
        exit /b 1
    )
)

REM 创建必要的目录
if not exist "data\raw_data" mkdir "data\raw_data"
if not exist "data\temp_data" mkdir "data\temp_data"
if not exist "data\backup_data" mkdir "data\backup_data"
if not exist "data\output_data" mkdir "data\output_data"
if not exist "logs" mkdir "logs"

echo 目录结构检查完成
echo.

REM 询问用户是否先运行测试
set /p choice="是否先运行系统测试？(y/n): "
if /i "%choice%"=="y" (
    echo.
    echo 运行系统测试...
    python test_system.py
    if errorlevel 1 (
        echo.
        echo 测试失败，请检查配置和数据文件
        pause
        exit /b 1
    )
    echo.
    echo 测试完成，按任意键继续运行正式校验...
    pause >nul
)

echo.
echo 开始运行金豆发放申请校验系统...
echo ================================================================
echo.

REM 运行主程序
python run_validation.py

REM 检查运行结果
if errorlevel 1 (
    echo.
    echo ================================================================
    echo 校验过程中发现问题，请查看上方输出信息和日志文件
    echo 日志文件位置: logs\validation_system.log
    echo 报告文件位置: data\output_data\
    echo ================================================================
) else (
    echo.
    echo ================================================================
    echo 校验成功完成！
    echo 处理结果已保存到 data\output_data\ 目录
    echo 备份文件已保存到 data\backup_data\ 目录
    echo ================================================================
)

echo.
echo 按任意键退出...
pause >nul
