"""
金豆发放申请校验系统 - 数据清洗模块
负责数据的清洗、转换和格式标准化

本模块是数据处理流程的第一个关键环节，主要功能包括：

核心功能：
1. 数据格式标准化：统一日期格式、数值格式、字符串格式
2. 数据质量检查：验证必填字段、数据格式、业务规则
3. 异常数据识别：识别和记录各种数据质量问题
4. 批量文件处理：支持多个申请明细表文件的批量清洗
5. 临时汇总表生成：将清洗后的数据汇总为统一格式

处理流程：
文件读取 -> 业务规则过滤 -> 空行移除 -> 字符串清理 -> 日期标准化 -> 格式验证 -> 必填字段检查

设计特点：
- 管道式处理：数据依次通过各个清洗步骤
- 问题收集：收集所有数据质量问题，统一报告
- 容错处理：部分数据问题不影响整体处理
- 性能监控：监控各个处理阶段的性能

"""

# 导入标准库
import pandas as pd  # 数据处理核心库
import numpy as np   # 数值计算库
import re           # 正则表达式
from typing import Dict, List, Tuple, Any, Optional  # 类型注解
from pathlib import Path  # 路径操作
import logging      # 日志记录

# 导入自定义工具类
from utils import ConfigManager, LoggerSetup, DataValidator, PerformanceMonitor


class DataQualityIssue:
    """
    数据质量问题记录类
    
    用于记录数据清洗过程中发现的各种质量问题，包括：
    - 格式错误：日期格式、手机号格式、金额格式等
    - 必填字段缺失：关键字段为空或无效
    - 数据类型错误：应为数字的字段包含文本等
    - 业务规则违反：不符合业务逻辑的数据
    
    每个问题记录包含完整的上下文信息，便于后续分析和修复。
    """
    
    def __init__(self, file_name: str, row_index: int, column: str, 
                 issue_type: str, description: str, value: Any = None):
        """
        初始化数据质量问题记录
        
        Args:
            file_name: 发生问题的文件名
            row_index: 问题所在的行索引（pandas索引，从0开始）
            column: 问题所在的列名
            issue_type: 问题类型（如"日期格式错误"、"必填字段缺失"等）
            description: 问题的详细描述
            value: 引起问题的具体数值（可选）
            
        记录的信息：
        - 问题位置：文件名、行号、列名
        - 问题性质：类型、描述、具体值
        - 发现时间：便于追踪问题发现的时间点
        """
        self.file_name = file_name      # 问题文件名
        self.row_index = row_index      # 行索引（从0开始）
        self.column = column            # 列名
        self.issue_type = issue_type    # 问题类型
        self.description = description  # 问题描述
        self.value = value             # 问题值
        self.timestamp = pd.Timestamp.now()  # 发现时间戳
    
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典格式
        
        将问题记录转换为字典格式，便于：
        - 生成数据质量报告
        - 序列化为JSON或CSV
        - 在日志中记录
        
        Returns:
            Dict[str, Any]: 包含所有问题信息的字典
            
        注意：
        - 行号转换：pandas索引从0开始，但用户看到的行号从1开始，
          且要考虑表头，所以实际行号 = pandas索引 + 2
        """
        return {
            '文件名': self.file_name,
            '行号': self.row_index + 2,  # +2: pandas索引从0开始 + 表头占1行
            '列名': self.column,
            '问题类型': self.issue_type,
            '问题描述': self.description,
            '问题值': self.value,
            '发现时间': self.timestamp
        }


class DataCleaner:
    """数据清洗器"""
    
    def __init__(self, config_manager: ConfigManager):
        """
        初始化数据清洗器
        
        Args:
            config_manager: 配置管理器实例
        """
        self.config = config_manager
        self.logger = LoggerSetup.setup_logger('DataCleaner', 
                                             config_manager.get('logging', {}))
        self.validator = DataValidator(config_manager)
        self.monitor = PerformanceMonitor(config_manager)
        self.quality_issues: List[DataQualityIssue] = []
    
    def clean_single_file(self, file_path: str) -> Tuple[pd.DataFrame, List[DataQualityIssue]]:
        """
        清洗单个文件
        
        这是数据清洗的核心方法，按照标准流程对单个文件进行完整的清洗处理。
        
        清洗流程：
        1. 安全读取文件（支持多种编码）
        2. 过滤业务规则说明行
        3. 识别和移除空行
        4. 清理字符串字段（去除空格等）
        5. 标准化日期格式
        6. 验证数据格式（手机号、金额等）
        7. 检查必填字段完整性
        
        Args:
            file_path: 要清洗的文件路径
            
        Returns:
            Tuple[pd.DataFrame, List[DataQualityIssue]]: 
                - 清洗后的数据框
                - 发现的数据质量问题列表
                
        Raises:
            FileNotFoundError: 文件不存在
            pd.errors.EmptyDataError: 文件为空
            Exception: 其他处理错误（编码错误、格式错误等）
            
        性能监控：
        - 监控每个清洗步骤的耗时
        - 记录性能检查点便于优化
        """
        try:
            # 开始性能监控，使用文件名作为监控标识
            self.monitor.start_monitoring(f"清洗文件: {Path(file_path).name}")
            file_issues = []  # 初始化问题列表
            
            # ========== 步骤1: 读取文件 ==========
            self.logger.info(f"开始清洗文件: {file_path}")
            df = self._read_file_safely(file_path)  # 安全读取文件
            
            # 检查文件是否成功读取
            if df is None or df.empty:
                self.logger.warning(f"文件为空或无法读取: {file_path}")
                return pd.DataFrame(), file_issues  # 返回空结果
            
            self.monitor.checkpoint("文件读取完成")  # 记录性能检查点
            
            # ========== 步骤2: 过滤业务规则说明 ==========
            # 移除文件中的业务规则说明行，这些行不是实际数据
            df_filtered = self._filter_business_rules(df, Path(file_path).name)
            self.monitor.checkpoint("业务规则过滤完成")
            
            # ========== 步骤3: 识别和移除空行 ==========
            # 移除完全为空或关键字段为空的行
            df_no_empty = self._remove_empty_rows(df_filtered)
            self.monitor.checkpoint("空行移除完成")
            
            # ========== 步骤4: 字符串清理 ==========
            # 清理字符串字段：去除前后空格、处理特殊字符等
            df_cleaned = self._clean_string_fields(df_no_empty)
            self.monitor.checkpoint("字符串清理完成")
            
            # ========== 步骤5: 日期格式标准化 ==========
            # 将各种日期格式统一转换为YYYY-MM-DD格式
            df_dates_fixed, date_issues = self._standardize_dates(df_cleaned, Path(file_path).name)
            file_issues.extend(date_issues)  # 添加日期问题到总问题列表
            self.monitor.checkpoint("日期标准化完成")
            
            # ========== 步骤6: 数据格式校验 ==========
            # 验证手机号、金额等字段的格式是否正确
            validation_issues = self._validate_data_formats(df_dates_fixed, Path(file_path).name)
            file_issues.extend(validation_issues)  # 添加格式问题到总问题列表
            self.monitor.checkpoint("数据格式校验完成")
            
            # ========== 步骤7: 必填字段检查 ==========
            # 检查必填字段是否完整，包括条件必填字段
            required_issues = self._check_required_fields(df_dates_fixed, Path(file_path).name)
            file_issues.extend(required_issues)  # 添加必填字段问题到总问题列表
            self.monitor.checkpoint("必填字段检查完成")
            
            # 结束性能监控
            self.monitor.end_monitoring(f"清洗文件: {Path(file_path).name}")
            
            # 记录清洗完成日志
            self.logger.info(f"文件清洗完成: {file_path}, 发现 {len(file_issues)} 个数据质量问题")
            return df_dates_fixed, file_issues  # 返回清洗结果
            
        except Exception as e:
            # 捕获并记录清洗过程中的异常
            self.logger.error(f"清洗文件时发生错误: {file_path}, 错误: {e}")
            raise  # 重新抛出异常，让调用者处理
    
    def _read_file_safely(self, file_path: str) -> Optional[pd.DataFrame]:
        """
        安全读取文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            数据框或None
        """
        try:
            # 尝试不同的编码格式
            encodings = ['utf-8-sig', 'utf-8', 'gbk', 'gb2312']
            
            # 指定需要保持为字符串类型的列
            dtype_dict = {
                '客户手机号': str,
                '客户名称': str,
                '客户归属企业': str,
                '申请发放机构': str,
                '活动方案全称': str,
                '归属条线': str,
                '备注（非必填）': str
            }
            
            for encoding in encodings:
                try:
                    df = pd.read_csv(file_path, encoding=encoding, dtype=dtype_dict)
                    self.logger.debug(f"成功使用编码 {encoding} 读取文件: {file_path}")
                    return df
                except UnicodeDecodeError:
                    continue
            
            self.logger.error(f"无法使用任何编码读取文件: {file_path}")
            return None
            
        except Exception as e:
            self.logger.error(f"读取文件时发生错误: {file_path}, 错误: {e}")
            return None
    
    def _filter_business_rules(self, df: pd.DataFrame, file_name: str) -> pd.DataFrame:
        """
        过滤业务规则说明信息
        
        Args:
            df: 原始数据框
            file_name: 文件名
            
        Returns:
            过滤后的数据框
        """
        try:
            # 查找包含业务规则说明的行
            # 通常在第5行（索引4）开始，且包含特定的业务规则文本
            rule_indicators = ['记录ID', '客户归属企业', '申请发放机构', '申请发放日期', '申请填报日期']
            
            # 检查是否存在业务规则说明
            for idx, row in df.iterrows():
                if idx >= 4:  # 从第5行开始检查
                    # 如果行数大于0，则检查最后一列
                    row_str = str(row.iloc[-1]) if len(row) > 0 else ""
                    # 如果任意一个指示符在行中，则认为存在业务规则说明
                    if any(indicator in row_str for indicator in rule_indicators):
                        self.logger.info(f"在文件 {file_name} 第 {idx + 2} 行发现业务规则说明，将过滤该行及之后的规则说明")
                        return df.iloc[:idx].copy()
            
            return df.copy()
            
        except Exception as e:
            self.logger.warning(f"过滤业务规则说明时发生错误: {e}，返回原始数据")
            return df.copy()
    
    def _remove_empty_rows(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        移除空行
        
        Args:
            df: 数据框
            
        Returns:
            移除空行后的数据框
        """
        try:
            # 识别完全为空的行（所有字段都为空或NaN）
            empty_mask = df.isnull().all(axis=1) | (df == '').all(axis=1)
            
            # 识别只有记录ID但其他关键字段为空的行
            key_columns = ['客户名称', '客户手机号', '等值金豆发放数量', '申请发放机构']
            existing_key_columns = [col for col in key_columns if col in df.columns]
            
            if existing_key_columns:
                key_empty_mask = df[existing_key_columns].isnull().all(axis=1) | \
                               (df[existing_key_columns] == '').all(axis=1)
                empty_mask = empty_mask | key_empty_mask
            
            empty_count = empty_mask.sum()
            if empty_count > 0:
                self.logger.info(f"识别到 {empty_count} 个空行，将被移除")
            
            return df[~empty_mask].copy()
            
        except Exception as e:
            self.logger.warning(f"移除空行时发生错误: {e}，返回原始数据")
            return df.copy()
    
    def _clean_string_fields(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        清理字符串字段
        
        Args:
            df: 数据框
            
        Returns:
            清理后的数据框
        """
        try:
            df_cleaned = df.copy()
            string_fields = self.config.get('data_cleaning.string_fields_to_clean', [])
            
            for field in string_fields:
                if field in df_cleaned.columns:
                    # 去除前后空格，但保留None/NaN值
                    df_cleaned[field] = df_cleaned[field].apply(
                        lambda x: x.strip() if isinstance(x, str) else x
                    )
                    self.logger.debug(f"清理字段: {field}")
            
            # 特殊处理手机号字段，确保去除小数点
            if '客户手机号' in df_cleaned.columns:
                df_cleaned['客户手机号'] = df_cleaned['客户手机号'].apply(
                    lambda x: str(x).replace('.0', '').replace('.', '').strip() if pd.notna(x) and x != '' else x
                )
                self.logger.debug("特殊处理手机号字段，去除小数点")
            
            return df_cleaned
            
        except Exception as e:
            self.logger.warning(f"清理字符串字段时发生错误: {e}，返回原始数据")
            return df.copy()
    
    def _standardize_dates(self, df: pd.DataFrame, file_name: str) -> Tuple[pd.DataFrame, List[DataQualityIssue]]:
        """
        标准化日期格式
        
        Args:
            df: 数据框
            file_name: 文件名
            
        Returns:
            (标准化后的数据框, 日期问题列表)
        """
        try:
            df_standardized = df.copy()
            date_issues = []
            date_columns = ['申请发放日期', '申请填报日期']
            
            for col in date_columns:
                if col in df_standardized.columns:
                    for idx, date_value in df_standardized[col].items():
                        if pd.notna(date_value) and date_value != '':
                            is_valid, standardized_date = self.validator.validate_date_format(str(date_value))
                            
                            if is_valid:
                                df_standardized.loc[idx, col] = standardized_date
                            else:
                                issue = DataQualityIssue(
                                    file_name=file_name,
                                    row_index=idx,
                                    column=col,
                                    issue_type="日期格式错误",
                                    description=f"无法解析的日期格式",
                                    value=date_value
                                )
                                date_issues.append(issue)
            
            return df_standardized, date_issues
            
        except Exception as e:
            self.logger.warning(f"标准化日期格式时发生错误: {e}")
            return df.copy(), []

    def _validate_data_formats(self, df: pd.DataFrame, file_name: str) -> List[DataQualityIssue]:
        """
        验证数据格式

        Args:
            df: 数据框
            file_name: 文件名

        Returns:
            数据格式问题列表
        """
        try:
            format_issues = []

            # 验证手机号格式
            if '客户手机号' in df.columns:
                for idx, phone in df['客户手机号'].items():
                    if pd.notna(phone) and phone != '':
                        # 处理手机号格式，确保是字符串且去除小数点
                        phone_str = str(phone).replace('.0', '').strip()
                        if not self.validator.validate_phone_number(phone_str):
                            issue = DataQualityIssue(
                                file_name=file_name,
                                row_index=idx,
                                column='客户手机号',
                                issue_type="手机号格式错误",
                                description="手机号格式不符合规范",
                                value=phone
                            )
                            format_issues.append(issue)

            # 验证等值金豆发放数量
            if '等值金豆发放数量' in df.columns:
                for idx, amount in df['等值金豆发放数量'].items():
                    if pd.notna(amount) and amount != '':
                        if not self.validator.validate_amount(amount):
                            issue = DataQualityIssue(
                                file_name=file_name,
                                row_index=idx,
                                column='等值金豆发放数量',
                                issue_type="金额格式错误",
                                description="等值金豆发放数量必须为有效正数",
                                value=amount
                            )
                            format_issues.append(issue)

            return format_issues

        except Exception as e:
            self.logger.warning(f"验证数据格式时发生错误: {e}")
            return []

    def _check_required_fields(self, df: pd.DataFrame, file_name: str) -> List[DataQualityIssue]:
        """
        检查必填字段

        Args:
            df: 数据框
            file_name: 文件名

        Returns:
            必填字段问题列表
        """
        try:
            required_issues = []
            required_fields = self.config.get('data_cleaning.validation_rules.required_fields', [])

            # 检查基本必填字段
            for field in required_fields:
                if field in df.columns:
                    for idx, value in df[field].items():
                        if pd.isna(value) or value == '' or value is None:
                            issue = DataQualityIssue(
                                file_name=file_name,
                                row_index=idx,
                                column=field,
                                issue_type="必填字段缺失",
                                description=f"必填字段 {field} 不能为空",
                                value=value
                            )
                            required_issues.append(issue)

            # 检查条件必填字段
            conditional_required = self.config.get('data_cleaning.validation_rules.conditional_required', [])
            for rule in conditional_required:
                field = rule.get('field')
                condition = rule.get('condition')

                if field in df.columns and '归属条线' in df.columns:
                    # 解析条件：归属条线 == '公司'
                    if condition == "归属条线 == '公司'":
                        for idx, row in df.iterrows():
                            if row['归属条线'] == '公司':
                                if pd.isna(row[field]) or row[field] == '' or row[field] is None:
                                    issue = DataQualityIssue(
                                        file_name=file_name,
                                        row_index=idx,
                                        column=field,
                                        issue_type="条件必填字段缺失",
                                        description=f"当归属条线为'公司'时，{field} 字段必填",
                                        value=row[field]
                                    )
                                    required_issues.append(issue)

            return required_issues

        except Exception as e:
            self.logger.warning(f"检查必填字段时发生错误: {e}")
            return []

    def clean_multiple_files(self, file_paths: List[str]) -> Tuple[pd.DataFrame, List[DataQualityIssue]]:
        """
        清洗多个文件并汇总

        Args:
            file_paths: 文件路径列表

        Returns:
            (汇总的清洗后数据框, 所有数据质量问题列表)

        Raises:
            Exception: 清洗过程中的错误
        """
        try:
            self.monitor.start_monitoring("批量文件清洗")
            # 创建一个空的数据框
            all_dataframes = []
            # 创建一个空列表，用于存储所有数据质量问题
            all_issues = []

            # 遍历文件列表
            for i, file_path in enumerate(file_paths, 1):
                try:
                    self.logger.info(f"正在处理第 {i}/{len(file_paths)} 个文件: {Path(file_path).name}")

                    # 清洗单个文件
                    df_cleaned, file_issues = self.clean_single_file(file_path)

                    # 检查数据
                    if not df_cleaned.empty:
                        # 添加源文件信息
                        df_cleaned['源文件名'] = Path(file_path).name
                        all_dataframes.append(df_cleaned)

                    # 添加数据质量问题，extend指将列表中的元素添加到all_issues中
                    all_issues.extend(file_issues)

                    self.monitor.checkpoint(f"文件 {i} 处理完成")

                except Exception as e:
                    self.logger.error(f"处理文件 {file_path} 时发生错误: {e}")
                    # 继续处理其他文件
                    continue

            # 汇总所有数据
            if all_dataframes:
                combined_df = pd.concat(all_dataframes, ignore_index=True)
                self.logger.info(f"成功汇总 {len(all_dataframes)} 个文件的数据，共 {len(combined_df)} 条记录")
            else:
                combined_df = pd.DataFrame()
                self.logger.warning("没有成功处理任何文件")

            self.monitor.end_monitoring("批量文件清洗")

            return combined_df, all_issues

        except Exception as e:
            self.logger.error(f"批量清洗文件时发生错误: {e}")
            raise

    def generate_quality_report(self, issues: List[DataQualityIssue]) -> pd.DataFrame:
        """
        生成数据质量报告

        Args:
            issues: 数据质量问题列表

        Returns:
            数据质量报告数据框
        """
        try:
            if not issues:
                self.logger.info("没有发现数据质量问题")
                return pd.DataFrame()

            # 从问题列表生成报告数据，并转换为数据框
            report_data = [issue.to_dict() for issue in issues]
            report_df = pd.DataFrame(report_data)

            # 按问题类型统计
            issue_summary = report_df.groupby('问题类型').size().reset_index(name='问题数量')

            self.logger.info(f"数据质量报告生成完成，共发现 {len(issues)} 个问题")
            self.logger.info("问题类型统计:")
            for _, row in issue_summary.iterrows():
                self.logger.info(f"  {row['问题类型']}: {row['问题数量']} 个")

            return report_df

        except Exception as e:
            self.logger.error(f"生成数据质量报告时发生错误: {e}")
            return pd.DataFrame()

    def save_temp_summary_table(self, df: pd.DataFrame) -> str:
        """
        保存临时汇总表

        Args:
            df: 汇总数据框

        Returns:
            保存的文件路径

        Raises:
            Exception: 保存过程中的错误
        """
        try:
            if df.empty:
                raise ValueError("数据框为空，无法保存临时汇总表")

            # 生成记录ID
            df_with_id = df.copy()
            df_with_id['记录ID'] = range(1, len(df_with_id) + 1)

            # 调整列顺序，将记录ID放在第一列
            columns = ['记录ID'] + [col for col in df_with_id.columns if col != '记录ID']
            df_with_id = df_with_id[columns]

            # 保存文件
            raw_data_path = self.config.get('data_paths.raw_data')
            temp_table_name = self.config.get('table_files.temp_summary_table')
            file_path = Path(raw_data_path) / temp_table_name

            df_with_id.to_csv(file_path, index=False, encoding='utf-8-sig')

            self.logger.info(f"临时汇总表保存成功: {file_path}, 共 {len(df_with_id)} 条记录")
            return str(file_path)

        except Exception as e:
            self.logger.error(f"保存临时汇总表时发生错误: {e}")
            raise


# TODO: 扩展点 - 数据清洗功能扩展
# 1. 数据类型自动推断和转换
# 2. 异常值检测和处理
# 3. 数据标准化和归一化
# 4. 重复数据预处理
# 5. 数据质量评分机制
